{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h2><i class="fas fa-images"></i> Faculty Images</h2>
            <p class="text-muted">View registered faculty photos and their encoding status</p>
        </div>
    </div>

    <div class="row">
        {% for faculty in faculty_list %}
        <div class="col-md-4 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">{{ faculty.name }}</h5>
                </div>
                {% if faculty.image_exists %}
                <div class="position-relative">
                    <img src="{{ url_for('get_faculty_image', faculty_id=faculty.id) }}" class="card-img-top" alt="{{ faculty.name }}'s photo">
                    <small class="position-absolute bottom-0 end-0 bg-dark text-white p-1 m-1 rounded">
                        {{ faculty.image_path }}
                    </small>
                </div>
                {% else %}
                <div class="text-center p-4 bg-light">
                    <i class="fas fa-user-circle fa-5x text-muted"></i>
                    <p class="mt-2 text-muted">No image available</p>
                </div>
                {% endif %}
                <div class="card-body">
                    <p><strong>Faculty ID:</strong> {{ faculty.id }}</p>
                    <p><strong>Encoding Status:</strong> 
                        {% if faculty.is_encoded %}
                        <span class="badge bg-success">Encoded</span>
                        {% else %}
                        <span class="badge bg-warning">Not Encoded</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="row mt-3">
        <div class="col">
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %} 