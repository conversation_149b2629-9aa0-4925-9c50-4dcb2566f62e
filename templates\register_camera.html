{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-plus"></i> Register New Faculty</h5>
                </div>
                <div class="card-body">
                    {% if error %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}

                    {% if success %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> {{ success }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}

                    <form method="POST" action="{{ url_for('register_camera') }}" id="registerForm">
                        <div class="row">
                            <!-- Left Column - Faculty Information -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-user"></i> Faculty Information</h5>
                                <hr>

                                <div class="mb-3">
                                    <label for="faculty_id" class="form-label">Faculty ID</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                        <input type="text" class="form-control" id="faculty_id" name="faculty_id" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                </div>

                                <!-- Department field removed -->
                                <input type="hidden" name="department" value="CICT">

                                <div class="mb-3">
                                    <label for="position" class="form-label">Position</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                        <select class="form-select" id="position" name="position" required>
                                            <option value="" selected disabled>Select position</option>
                                            <option value="Staff">Staff</option>
                                            <option value="Faculty">Faculty</option>
                                            <option value="Admin">Admin</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="contact_number" class="form-label">Contact Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="text" class="form-control" id="contact_number" name="contact_number" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Authentication Information and Camera -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-lock"></i> Authentication Information</h5>
                                <hr>

                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary toggle-password" type="button" onclick="togglePassword('password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Password must be at least 8 characters long.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <button class="btn btn-outline-secondary toggle-password" type="button" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Admin type section removed as there is only one super admin -->
                                <input type="hidden" name="admin_type" value="admin_user">

                                <!-- Hidden field to store the captured image data -->
                                <input type="hidden" name="captured_image" id="captured_image">
                            </div>
                        </div>

                        <!-- Camera Section -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <h5><i class="fas fa-camera"></i> Capture Faculty Image</h5>
                                <hr>
                                <div class="text-center">
                                    <div class="mb-3">
                                        <div class="video-container border rounded p-2 mx-auto" style="max-width: 480px;">
                                            <!-- Replace with server-side video feed -->
                                            <img id="videoFeed" src="{{ url_for('registration_video') }}" class="img-fluid rounded">
                                        </div>
                                    </div>
                                    <button type="button" id="captureBtn" class="btn btn-primary mb-3">
                                        <i class="fas fa-camera"></i> Capture Photo
                                    </button>
                                    <div id="previewContainer" class="mb-3" style="display:none;">
                                        <h6>Captured Image:</h6>
                                        <img id="previewImage" class="img-thumbnail" style="max-height: 200px;">
                                        <div class="mt-2">
                                            <button type="button" id="retakeBtn" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> Retake Photo
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12 d-flex justify-content-between">
                                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                                <button type="submit" id="submitBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-save"></i> Register Faculty
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const captureBtn = document.getElementById('captureBtn');
        const retakeBtn = document.getElementById('retakeBtn');
        const previewImage = document.getElementById('previewImage');
        const previewContainer = document.getElementById('previewContainer');
        const capturedImageInput = document.getElementById('captured_image');
        const submitBtn = document.getElementById('submitBtn');
        const videoFeed = document.getElementById('videoFeed');

        // Admin type section is now always visible if user is super admin

        // Function to reset the video feed
        function resetVideoFeed() {
            videoFeed.src = "{{ url_for('registration_video') }}?t=" + new Date().getTime();
        }

        // Simple capture photo function
        captureBtn.addEventListener('click', function() {
            // Show loading state
            captureBtn.disabled = true;
            captureBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Capturing...';

            // Clear any previous error messages
            const previousError = document.getElementById('captureError');
            if (previousError) {
                previousError.remove();
            }

            // Simple fetch request
            fetch('{{ url_for("capture_registration_image") }}')
                .then(response => response.json())
                .then(data => {
                    // Reset button state
                    captureBtn.disabled = false;
                    captureBtn.innerHTML = '<i class="fas fa-camera"></i> Capture Photo';

                    if (data.success) {
                        // Display the captured image
                        previewImage.src = data.image;
                        previewContainer.style.display = 'block';

                        // Store image data in hidden input
                        capturedImageInput.value = data.image;

                        // Enable submit button
                        submitBtn.disabled = false;
                    } else {
                        // Show simple error message
                        const errorDiv = document.createElement('div');
                        errorDiv.id = 'captureError';
                        errorDiv.className = 'alert alert-danger mt-2';
                        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${data.error}`;
                        captureBtn.parentNode.appendChild(errorDiv);
                        resetVideoFeed();
                    }
                })
                .catch(error => {
                    // Reset button state
                    captureBtn.disabled = false;
                    captureBtn.innerHTML = '<i class="fas fa-camera"></i> Capture Photo';

                    // Show simple error message
                    const errorDiv = document.createElement('div');
                    errorDiv.id = 'captureError';
                    errorDiv.className = 'alert alert-danger mt-2';
                    errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Failed to capture image. Please try again.';
                    captureBtn.parentNode.appendChild(errorDiv);
                    resetVideoFeed();
                });
        });

        // Retake photo
        retakeBtn.addEventListener('click', function() {
            previewContainer.style.display = 'none';
            capturedImageInput.value = '';
            submitBtn.disabled = true;
            resetVideoFeed();
        });
    });

    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const button = input.parentElement.querySelector('.toggle-password');
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
</script>
{% endblock %}