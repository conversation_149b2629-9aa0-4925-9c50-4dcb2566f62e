/* Face Recognition Page Styling */
:root {
  --primary-color: #4e73df;
  --secondary-color: #36b9cc;
  --success-color: #1cc88a;
  --dark-color: #2e59d9;
  --light-color: #f8f9fc;
  --danger-color: #e74a3b;
  --warning-color: #f6c23e;
  --border-radius: 10px;
  --box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  --animation-time: 3s;
}

.face-recognition-container {
  min-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #eef2f7 100%);
  overflow-x: hidden;
  position: relative;
}

/* Logo Styling */
.logo-container {
  margin-bottom: 30px;
  text-align: center;
  animation: fade-in 1s ease-out;
}

.logo-text {
  font-size: 4rem;
  font-weight: 800;
  letter-spacing: 0.5rem;
  color: var(--primary-color);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 5px;
}

.logo-subtext {
  font-size: 1rem;
  color: #6c757d;
  letter-spacing: 0.1rem;
  text-transform: uppercase;
}

/* Layout for recognition UI */
.recognition-layout {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  transition: all 0s linear;
  gap: 30px;
  will-change: transform;
}

/* Initial centered state */
.recognition-layout.centered {
  justify-content: center;
}

/* Active recognition state */
.recognition-layout.active {
  justify-content: center;
}

/* Video Container Styling */
.video-wrapper {
  position: relative;
  width: 100%;
  max-width: 640px;
  margin: 0;
  margin-left: 350px;
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: center;
}

.recognition-layout.centered .video-wrapper {
  margin: 0 auto;
  margin-left: 350px;
}

.recognition-layout.active .video-wrapper {
  transform: none;
  margin: 0;
}

.video-container {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  background-color: #000;
  min-width: 500px;
}

.video-feed {
  display: block;
  width: 100%;
  height: auto;
  transform-origin: center;
  opacity: 0;
  transition: opacity 0.1s ease-in;
}

.video-feed.loaded {
  opacity: 1;
}

/* Faculty Information Panel */
.faculty-info-panel {
  flex: 1;
  max-width: 400px;
  min-width: 320px;
  transform: translateX(120%);
  transition: transform 0.1s ease-out, opacity 0.01s linear, visibility 0s linear;
  opacity: 0;
  visibility: hidden;
  will-change: transform, opacity, visibility;
}

.recognition-layout.active .faculty-info-panel {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out, visibility 0s linear;
}

.faculty-info-panel.hidden {
  transform: translateX(50px);
  opacity: 0;
  visibility: hidden;
  transition: transform 0s linear, opacity 0s linear, visibility 0s linear;
}

.faculty-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.03s ease-out;
}

.faculty-header {
  background-color: var(--primary-color);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faculty-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 700;
  letter-spacing: 1px;
}

.status-badge.active {
  background-color: var(--success-color);
  color: white;
}

.status-badge.inactive {
  background-color: var(--danger-color);
  color: white;
}

.faculty-profile {
  padding: 20px;
  display: flex;
  gap: 20px;
  border-bottom: 1px solid #eee;
}

.faculty-image-container {
  width: 100px;
  height: 120px;
  overflow: hidden;
  border-radius: 5px;
  border: 2px solid #eee;
  flex-shrink: 0;
}

.faculty-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.faculty-details {
  flex: 1;
}

.faculty-details h2 {
  margin: 0 0 10px 0;
  font-size: 1.4rem;
  color: #333;
}

.faculty-details p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

.attendance-info {
  padding: 20px;
  flex: 1;
}

.attendance-status h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.time-record {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.time-label {
  color: #666;
  font-weight: 500;
}

.time-value {
  color: #333;
  font-weight: 600;
}

.timeIn-recorded {
  background-color: var(--success-color);
  color: white;
  text-align: center;
  padding: 10px;
  position: relative;
  overflow: hidden;
  height: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.timeIn-recorded.visible {
  height: auto;
  opacity: 1;
  padding: 15px;
}

.recording-animation {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.5);
  animation: recording-scan 1.5s ease-in-out infinite;
}

/* Scan line effect */
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
  animation: scan-line var(--animation-time) linear infinite;
  opacity: 0.7;
  z-index: 5;
}

/* Corner decorations */
.corner {
  position: absolute;
  width: 30px;
  height: 30px;
  border-color: var(--primary-color);
  border-style: solid;
  border-width: 0;
  z-index: 5;
}

.top-left {
  top: 10px;
  left: 10px;
  border-top-width: 3px;
  border-left-width: 3px;
  border-radius: 8px 0 0 0;
}

.top-right {
  top: 10px;
  right: 10px;
  border-top-width: 3px;
  border-right-width: 3px;
  border-radius: 0 8px 0 0;
}

.bottom-left {
  bottom: 10px;
  left: 10px;
  border-bottom-width: 3px;
  border-left-width: 3px;
  border-radius: 0 0 0 8px;
}

.bottom-right {
  bottom: 10px;
  right: 10px;
  border-bottom-width: 3px;
  border-right-width: 3px;
  border-radius: 0 0 8px 0;
}

/* Status indicator */
.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  font-weight: 500;
}

.status-circle {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #6c757d;
  transition: background-color 0.3s ease;
}

.status-circle.active {
  background-color: var(--success-color);
  box-shadow: 0 0 10px var(--success-color);
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 1rem;
  color: #555;
}

/* Instructions panel */
.instruction-panel {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  max-width: 800px;
  width: 100%;
  animation: fade-in 1s ease-out 0.5s both;
}

.instruction-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  flex: 1 1 200px;
  max-width: 250px;
}

.instruction-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.instruction-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-right: 15px;
}

.instruction-item span {
  font-size: 0.9rem;
  color: #555;
}

/* Recognition feedback */
.recognition-feedback {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--success-color);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease, visibility 0.4s ease;
}

.video-container.recognizing .recognition-feedback {
  opacity: 1;
  visibility: visible;
}

/* Animations */
@keyframes scan-line {
  0% {
    top: 0%;
  }
  50% {
    top: 96%;
  }
  100% {
    top: 0%;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes recording-scan {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .recognition-layout {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .recognition-layout.active .video-wrapper {
    transform: none;
    margin-bottom: 20px;
  }
  
  .recognition-layout.active .faculty-info-panel {
    transform: translateY(0);
  }
  
  .faculty-info-panel {
    transform: translateY(50px);
    margin-left: 0;
    width: 100%;
    max-width: 640px;
  }
}

@media (max-width: 768px) {
  .logo-text {
    font-size: 3rem;
  }
  
  .instruction-item {
    flex: 1 1 100%;
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  .logo-text {
    font-size: 2.5rem;
    letter-spacing: 0.3rem;
  }
  
  .corner {
    width: 20px;
    height: 20px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .face-recognition-container {
    background: linear-gradient(135deg, #1e2430 0%, #2c3e50 100%);
  }
  
  .logo-text {
    color: #fff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .logo-subtext {
    color: #adb5bd;
  }
  
  .instruction-item {
    background-color: #2c3e50;
  }
  
  .instruction-item span,
  .status-text {
    color: #e9ecef;
  }
}

/* Face Recognition Animation */
.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  z-index: 4;
  transition: border-color 0.3s ease;
}

.video-container.recognizing::before {
  border-color: var(--success-color);
  animation: border-pulse 1.5s infinite;
}

.face-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  border: 2px solid var(--secondary-color);
  border-radius: 50%;
  opacity: 0;
  z-index: 6;
  pointer-events: none;
}

.recognizing .face-overlay {
  animation: face-detect 2s ease-out forwards;
}

@keyframes face-detect {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  20% {
    width: 150px;
    height: 200px;
    opacity: 0.8;
  }
  80% {
    width: 150px;
    height: 200px;
    opacity: 0.8;
  }
  100% {
    width: 150px;
    height: 200px;
    opacity: 0;
  }
}

@keyframes border-pulse {
  0% {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0 rgba(28, 200, 138, 0.4);
  }
  70% {
    border-color: var(--success-color);
    box-shadow: 0 0 0 10px rgba(28, 200, 138, 0);
  }
  100% {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0 rgba(28, 200, 138, 0);
  }
}

.scan-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, 
    rgba(78, 115, 223, 0) 0%,
    rgba(78, 115, 223, 0.1) 50%,
    rgba(78, 115, 223, 0) 100%);
  opacity: 0;
  z-index: 4;
  animation: scan-effect 4s ease infinite;
}

@keyframes scan-effect {
  0% {
    top: -100%;
    opacity: 0.7;
  }
  100% {
    top: 100%;
    opacity: 0.7;
  }
}

.video-wrapper::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--primary-color);
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  opacity: 0;
}

.recognition-layout.centered .video-wrapper::after {
  opacity: 0.7;
}

.recognition-layout.active .video-wrapper::after {
  opacity: 0;
} 