from PIL import Image, ImageDraw
import os

def create_default_profile_image():
    """Create a proper default profile image"""
    # Image dimensions
    width, height = 200, 250
    
    # Create a new image with a light blue background
    img = Image.new('RGB', (width, height), color=(220, 230, 240))
    draw = ImageDraw.Draw(img)
    
    # Draw a silhouette or placeholder
    # Head circle
    head_radius = 40
    head_center = (width // 2, height // 3)
    draw.ellipse(
        (head_center[0] - head_radius, head_center[1] - head_radius,
         head_center[0] + head_radius, head_center[1] + head_radius),
        fill=(180, 190, 200)
    )
    
    # Body
    body_top = head_center[1] + head_radius
    body_width = head_radius * 2
    body_height = height - body_top - 30
    draw.rectangle(
        (head_center[0] - body_width//2, body_top,
         head_center[0] + body_width//2, body_top + body_height),
        fill=(180, 190, 200)
    )
    
    # Add text
    draw.text((width//2 - 35, height - 25), "No Image", fill=(100, 110, 120))
    
    # Save the image
    output_path = os.path.join('static', 'default-profile.jpg')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'JPEG')
    print(f"Created default profile image at {output_path}")

if __name__ == "__main__":
    create_default_profile_image() 