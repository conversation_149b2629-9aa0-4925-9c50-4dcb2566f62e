import os
import requests
from pathlib import Path

def download_file(url, local_path):
    """Download a file from URL and save it locally"""
    response = requests.get(url)
    os.makedirs(os.path.dirname(local_path), exist_ok=True)
    with open(local_path, 'wb') as f:
        f.write(response.content)

def setup_static_files():
    # Create static directory structure
    static_dir = Path('static')
    vendor_dir = static_dir / 'vendor'
    bootstrap_dir = vendor_dir / 'bootstrap'
    fontawesome_dir = vendor_dir / 'fontawesome'
    
    # Create directories
    for dir_path in [static_dir, vendor_dir, bootstrap_dir, fontawesome_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # Download Bootstrap files
    bootstrap_css_url = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'
    bootstrap_js_url = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'
    
    download_file(bootstrap_css_url, bootstrap_dir / 'css' / 'bootstrap.min.css')
    download_file(bootstrap_js_url, bootstrap_dir / 'js' / 'bootstrap.bundle.min.js')
    
    # Download Font Awesome files
    fontawesome_css_url = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css'
    fontawesome_webfonts_url = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2'
    
    download_file(fontawesome_css_url, fontawesome_dir / 'css' / 'all.min.css')
    download_file(fontawesome_webfonts_url, fontawesome_dir / 'webfonts' / 'fa-solid-900.woff2')
    
    print("Static files setup completed successfully!")

if __name__ == '__main__':
    setup_static_files() 