{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    {% if error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> {{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <div class="row">
        <!-- Video Feed Column -->
        <div class="col-md-8 mb-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-camera"></i> Face Recognition</h5>
                </div>
                <div class="card-body">
                    <div class="video-container">
                        <img src="{{ url_for('video') }}" class="img-fluid rounded" alt="Video Feed">
                    </div>
                    <div class="mt-3">
                        <div id="currentFaculty" class="alert alert-info" style="display: none;">
                            <i class="fas fa-user-check"></i> <span id="facultyInfo"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Information Column -->
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Information</h5>
                </div>
                <div class="card-body">
                    <p>Welcome to the Face Recognition Attendance System (FRASS).</p>
                    <p>This system uses computer vision to detect and recognize faculty members for attendance tracking.</p>
                    <p>If you're a faculty member or administrator, please login to access your dashboard:</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('login') }}" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Today's Stats -->
            <div class="card shadow mt-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Today's Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="p-2 border rounded text-center">
                                <h4 id="checkinsCount" class="text-primary mb-0">0</h4>
                                <small class="text-muted">Check-ins</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-2 border rounded text-center">
                                <h4 id="checkoutsCount" class="text-success mb-0">0</h4>
                                <small class="text-muted">Check-outs</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Function to update current faculty information
function updateCurrentFaculty() {
    fetch('/get_current_faculty')
        .then(response => response.json())
        .then(data => {
            const facultyInfo = document.getElementById('facultyInfo');
            const currentFaculty = document.getElementById('currentFaculty');
            const checkinsCount = document.getElementById('checkinsCount');
            const checkoutsCount = document.getElementById('checkoutsCount');
            
            if (data.name) {
                facultyInfo.textContent = `${data.name} (${data.department})`;
                currentFaculty.style.display = 'block';
            } else {
                currentFaculty.style.display = 'none';
            }
            
            // Update stats
            checkinsCount.textContent = data.checkins;
            checkoutsCount.textContent = data.checkouts;
        })
        .catch(error => console.error('Error:', error));
}

// Update current faculty every 2 seconds
setInterval(updateCurrentFaculty, 2000);
</script>
{% endblock %}
