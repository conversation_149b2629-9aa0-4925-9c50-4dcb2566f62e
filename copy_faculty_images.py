import os
import shutil
import glob
from PIL import Image

def copy_faculty_images():
    """
    Copy all faculty images from Images directory to static/faculty_images
    to make them accessible via the web server.
    """
    # Ensure the destination directory exists
    dest_dir = os.path.join('static', 'faculty_images')
    os.makedirs(dest_dir, exist_ok=True)
    
    # Copy default profile image if it doesn't exist
    default_path = os.path.join('static', 'default-profile.jpg')
    if not os.path.exists(default_path):
        # Create a simple default image
        img = Image.new('RGB', (150, 200), color=(200, 200, 200))
        img.save(default_path)
        print(f"Created default profile image at {default_path}")
    
    # Source patterns to check
    source_patterns = [
        os.path.join('Images', '*', 'profile.jpg'),  # Images/faculty_id/profile.jpg
        os.path.join('Images', '*.jpg'),             # Images/faculty_id.jpg
        os.path.join('Images', '*.png')              # Images/faculty_id.png
    ]
    
    # Keep track of processed faculty IDs
    processed_ids = set()
    
    # Process each pattern
    for pattern in source_patterns:
        for src_path in glob.glob(pattern):
            # Extract faculty ID
            if os.path.basename(src_path) == 'profile.jpg':
                # Images/faculty_id/profile.jpg pattern
                faculty_id = os.path.basename(os.path.dirname(src_path))
            else:
                # Images/faculty_id.jpg or Images/faculty_id.png pattern
                faculty_id = os.path.splitext(os.path.basename(src_path))[0]
            
            # Skip if already processed
            if faculty_id in processed_ids:
                continue
            
            # Destination path
            dest_path = os.path.join(dest_dir, f"{faculty_id}.jpg")
            
            try:
                # Copy and convert to jpg if needed
                if src_path.lower().endswith('.png'):
                    # Convert PNG to JPG
                    img = Image.open(src_path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(dest_path, 'JPEG')
                    print(f"Converted {src_path} to {dest_path}")
                else:
                    # Just copy the file
                    shutil.copy2(src_path, dest_path)
                    print(f"Copied {src_path} to {dest_path}")
                
                processed_ids.add(faculty_id)
                
            except Exception as e:
                print(f"Error processing {src_path}: {e}")
    
    print(f"Total faculty images processed: {len(processed_ids)}")
    
    # Also setup faculty images symlink for easy access in web server
    return processed_ids

if __name__ == "__main__":
    processed_ids = copy_faculty_images()
    print(f"Faculty IDs processed: {processed_ids}") 