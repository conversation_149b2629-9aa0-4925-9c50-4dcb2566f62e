import cv2
import face_recognition
import pickle
import os
import sqlite3

def init_encoding_db():
    """
    Initializes the SQLite database for storing faculty image encodings.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS encodings (
            faculty_id TEXT PRIMARY KEY,
            encoding BLOB NOT NULL,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')
    conn.commit()
    conn.close()

def save_encoding(faculty_id, encoding):
    """
    Saves the face encoding for a faculty member in the SQLite database.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO encodings (faculty_id, encoding)
        VALUES (?, ?)
    ''', (faculty_id, pickle.dumps(encoding)))
    conn.commit()
    conn.close()
    print(f"Encoding for faculty ID {faculty_id} saved successfully.")

def generate_encodings(image_directory):
    """
    Generates face encodings for all images in the specified directory.
    """
    for filename in os.listdir(image_directory):
        if filename.endswith('.jpg') or filename.endswith('.png'):
            path = os.path.join(image_directory, filename)
            img = cv2.imread(path)
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Detect and encode faces
            encodings = face_recognition.face_encodings(img_rgb)
            if encodings:
                faculty_id = os.path.splitext(filename)[0]  # Use filename (without extension) as faculty ID
                save_encoding(faculty_id, encodings[0])
            else:
                print(f"No face detected in {filename}.")

# Initialize the database
init_encoding_db()

# Example usage
# generate_encodings('path_to_image_directory')

# importing student images
folderPath = 'Images'
if not os.path.exists(folderPath):
    os.makedirs(folderPath)

pathList = os.listdir(folderPath)
print("Found images:", pathList)

imgList = []
facultyIds = []

for path in pathList:
    if path.endswith(('.jpg', '.png')):  # Support both jpg and png
        img = cv2.imread(os.path.join(folderPath, path))
        if img is not None:
            imgList.append(img)
            facultyIds.append(os.path.splitext(path)[0])
            print(f"Loaded image: {path}")
        else:
            print(f"Failed to load image: {path}")

print("Faculty IDs found:", facultyIds)

def findEncodings(imageList):
    encodeList = []
    for img in imageList:
        try:
            # Ensure proper RGB conversion
            if len(img.shape) == 2:  # If grayscale
                img_rgb = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
            else:
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Detect faces
            face_locations = face_recognition.face_locations(img_rgb)
            if not face_locations:
                print("No face detected in image")
                continue
                
            # Get face encodings
            face_encodings = face_recognition.face_encodings(img_rgb, face_locations)
            if face_encodings:
                encode = face_encodings[0]
                encodeList.append(encode)
                print("Successfully encoded a face")
            else:
                print("Could not encode face")
        except Exception as e:
            print(f"Error encoding face: {e}")
            continue
    return encodeList

print("Starting encodings...")
encodeListKnown = findEncodings(imgList)
encodeListKnownWithIds = (encodeListKnown, facultyIds)
print(f"Encodings complete. Found {len(encodeListKnown)} faces.")

# Save encodings only if we found some faces
if len(encodeListKnown) > 0:
    file = open("encodFile.p", 'wb')
    pickle.dump(encodeListKnownWithIds, file)
    file.close()
    print("Encoding file saved successfully")
else:
    print("No faces were encoded, skipping file save")