import cv2
import numpy as np
import face_recognition
from datetime import datetime
import sqlite3
import os
import pickle
from faculty_data import get_faculty_name
from attendance_records import update_time_in_out
import time

class Video:
    def __init__(self):
        # Initialize the video capture with fallback options
        self.video = None
        self.init_camera()

        # Initialize encodings
        self.encodeListKnown = []
        self.teacherIds = []
        self.load_encodings()

        # Initialize variables
        self.id = -1
        self.teacher_name = "Unknown"
        self.registration_mode = False  # Flag to indicate if we're in registration mode
        self._is_capturing = False  # Flag to track if we're in the middle of capturing

    def __del__(self):
        self.release()

    def release(self):
        """Release the camera resources"""
        try:
            if self.video is not None:
                self.video.release()
                self.video = None
            cv2.destroyAllWindows()
            self._is_capturing = False
        except Exception as e:
            print(f"Error releasing camera: {e}")

    def init_camera_simple(self):
        """Simple camera initialization for quick capture"""
        try:
            # Release any existing camera
            if self.video is not None:
                self.video.release()
                self.video = None

            # Simple direct initialization - just try camera index 0
            self.video = cv2.VideoCapture(0)

            if self.video is not None and self.video.isOpened():
                # Set basic properties
                self.video.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.video.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                print("Camera initialized successfully with simple method")
                return True
            else:
                print("Simple camera initialization failed")
                return False
        except Exception as e:
            print(f"Error in simple camera initialization: {e}")
            return False

    def init_camera(self):
        """Initialize the camera with better error handling and recovery"""
        try:
            # Release any existing camera first
            self.release()

            # Add a small delay to ensure previous resources are freed
            time.sleep(1.0)  # Increased delay for better resource cleanup

            # Try different camera indices and backends
            # First try without specifying backend (default)
            for index in [0, 1]:
                try:
                    print(f"Trying to initialize camera with index {index} (default backend)")
                    self.video = cv2.VideoCapture(index)
                    if self.video is not None and self.video.isOpened():
                        # Set camera properties
                        self.video.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        self.video.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                        self.video.set(cv2.CAP_PROP_FPS, 30)

                        # Test if we can read a frame
                        ret, frame = self.video.read()
                        if ret and frame is not None and frame.size > 0:
                            print(f"Successfully initialized camera with index {index} (default backend)")
                            return

                        self.release()
                except Exception as e:
                    print(f"Failed to initialize camera {index} (default backend): {e}")
                    self.release()

            # If default approach failed, try with specific backends
            backends = [cv2.CAP_DSHOW, cv2.CAP_ANY, cv2.CAP_MSMF, cv2.CAP_GSTREAMER]
            for index in [0, 1]:
                for backend in backends:
                    try:
                        print(f"Trying to initialize camera with index {index} and backend {backend}")
                        self.video = cv2.VideoCapture(index, backend)
                        if self.video is None or not self.video.isOpened():
                            continue

                        # Set camera properties
                        self.video.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                        self.video.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                        self.video.set(cv2.CAP_PROP_FPS, 30)

                        # Test if we can read a frame
                        ret, frame = self.video.read()
                        if ret and frame is not None and frame.size > 0:
                            print(f"Successfully initialized camera with index {index} and backend {backend}")
                            return

                        self.release()
                    except Exception as e:
                        print(f"Failed to initialize camera {index} with backend {backend}: {e}")
                        self.release()

            # Last resort: try with just index 0 and no backend, but with longer wait time
            try:
                print("Last resort camera initialization attempt")
                time.sleep(1.5)  # Longer wait before last attempt
                self.video = cv2.VideoCapture(0)
                time.sleep(0.5)  # Wait after initialization

                if self.video is not None and self.video.isOpened():
                    ret, frame = self.video.read()
                    if ret and frame is not None:
                        print("Successfully initialized camera with last resort method")
                        return
            except Exception as e:
                print(f"Last resort camera initialization failed: {e}")
                self.release()

            print("Warning: Failed to initialize any camera")
        except Exception as e:
            print(f"Error in camera initialization: {e}")
            self.release()

    def get_frame(self):
        """
        Captures a frame from the video feed, processes it for face recognition,
        and updates the SQLite database with attendance information.
        """
        try:
            if self._is_capturing:
                return None

            if self.video is None or not self.video.isOpened():
                print("Camera not initialized or closed")
                self.init_camera()  # Try to reinitialize
                if self.video is None or not self.video.isOpened():
                    return None

            # Capture a single frame
            success, img = self.video.read()
            if not success or img is None:
                print("Failed to capture frame")
                return None

            # In registration mode, just show the camera feed without face recognition
            if self.registration_mode:
                # Add guide overlay for face positioning
                height, width = img.shape[:2]
                # Draw face guideline rectangle
                guide_size = min(width, height) // 2
                center_x, center_y = width // 2, height // 2
                x1 = center_x - guide_size // 2
                y1 = center_y - guide_size // 2
                x2 = center_x + guide_size // 2
                y2 = center_y + guide_size // 2

                # Draw guide rectangle
                cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)

                # Add text instructions
                cv2.putText(img, "Position face inside the box", (x1, y1 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

                return self.encode_frame(img)

            # Normal face recognition mode (non-registration)
            # Resize and process the frame for face recognition
            imgS = cv2.resize(img, (0, 0), None, 0.25, 0.25)
            imgS = cv2.cvtColor(imgS, cv2.COLOR_BGR2RGB)

            # Only attempt face recognition if we have encodings
            if len(self.encodeListKnown) > 0:
                faceCurFrame = face_recognition.face_locations(imgS)
                if faceCurFrame:
                    encodeCurFrame = face_recognition.face_encodings(imgS, faceCurFrame)

                    for encodeFace, faceLoc in zip(encodeCurFrame, faceCurFrame):
                        matches = face_recognition.compare_faces(self.encodeListKnown, encodeFace, tolerance=0.5)
                        faceDis = face_recognition.face_distance(self.encodeListKnown, encodeFace)

                        if len(faceDis) > 0:  # Check if we have any face distances
                            matchIndex = np.argmin(faceDis)
                            # Only consider as a match if the confidence is high (lower distance is better)
                            confidence = 1 - faceDis[matchIndex]
                            if matches[matchIndex] and confidence > 0.5:
                                self.id = self.teacherIds[matchIndex]
                                self.teacher_name = self.get_faculty_name(self.id)
                                if self.teacher_name != "Unknown":
                                    # Draw rectangle around face
                                    y1, x2, y2, x1 = faceLoc
                                    y1, x2, y2, x1 = y1*4, x2*4, y2*4, x1*4
                                    cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                    cv2.putText(img, self.teacher_name, (x1, y1-10),
                                              cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

                                    # Update attendance
                                    update_time_in_out(self.id)

                                    # Update current session status for real-time display
                                    self.update_current_session(self.id)
                            else:
                                # No confident match found
                                self.id = -1
                                self.teacher_name = "Unknown"

            return self.encode_frame(img)

        except Exception as e:
            print(f"Error in get_frame: {e}")
            return None

    def capture_frame(self):
        """Capture a single frame for registration - simplified version"""
        try:
            # Use existing camera if available, or initialize a new one
            if self.video is None or not self.video.isOpened():
                print("Initializing camera for capture")
                self.init_camera_simple()

            if self.video is None or not self.video.isOpened():
                print("Failed to initialize camera")
                return None

            self._is_capturing = True
            self.registration_mode = True

            # Simple direct capture - one attempt
            print("Capturing frame...")
            success, img = self.video.read()

            if success and img is not None and img.size > 0:
                # Simple JPEG encoding
                ret, jpeg = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 90])
                if ret:
                    print("Successfully captured and encoded frame")
                    self._is_capturing = False
                    return jpeg.tobytes()

            print("Failed to capture valid frame")
            return None

        except Exception as e:
            print(f"Error in simple capture: {e}")
            return None
        finally:
            self._is_capturing = False

    def encode_frame(self, frame):
        """
        Encodes the given frame as JPEG with improved error handling.
        """
        try:
            if frame is None:
                print("Cannot encode None frame")
                return None

            if frame.size == 0:
                print("Cannot encode empty frame")
                return None

            # Ensure the frame is in the correct format
            if len(frame.shape) == 2:  # If grayscale
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            # Try to encode with different quality settings if needed
            for quality in [95, 85, 75, 65]:
                try:
                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
                    ret, jpeg = cv2.imencode('.jpg', frame, encode_param)
                    if ret and jpeg is not None and len(jpeg) > 0:
                        print(f"Successfully encoded frame with quality {quality}")
                        return jpeg.tobytes()
                except Exception as e:
                    print(f"Error encoding frame with quality {quality}: {e}")
                    continue

            # Last resort: try PNG encoding
            try:
                ret, png = cv2.imencode('.png', frame)
                if ret and png is not None and len(png) > 0:
                    print("Successfully encoded frame as PNG")
                    return png.tobytes()
            except Exception as e:
                print(f"Error encoding frame as PNG: {e}")

            print("All encoding attempts failed")
            return None
        except Exception as e:
            print(f"Error in encode_frame: {e}")
            return None

    def update_sqlite_time(self):
        """
        Update the time-in or time-out record for the detected teacher in SQLite.
        """
        current_time = datetime.now()
        current_date = current_time.strftime("%Y-%m-%d")
        current_hour = current_time.hour

        # Determine session type (morning or afternoon)
        session_type = 'morning' if current_hour < 12 else 'afternoon'

        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # Check if a time-in record exists for the current session
        cursor.execute('''
            SELECT time_in FROM attendance WHERE faculty_id = ? AND date = ? AND session = ?
        ''', (self.id, current_date, session_type))
        last_time_in = cursor.fetchone()

        if last_time_in:
            # Check if a time-out entry is needed
            cursor.execute('''
                SELECT time_out FROM attendance WHERE faculty_id = ? AND date = ? AND session = ?
            ''', (self.id, current_date, session_type))
            last_time_out = cursor.fetchone()

            if not last_time_out:
                # Record time-out
                cursor.execute('''
                    UPDATE attendance SET time_out = ? WHERE faculty_id = ? AND date = ? AND session = ?
                ''', (current_time.strftime("%I:%M:%S %p"), self.id, current_date, session_type))
                print(f"Time-out recorded for {session_type} session.")
        else:
            # Record time-in
            cursor.execute('''
                INSERT INTO attendance (faculty_id, date, session, time_in) VALUES (?, ?, ?, ?)
            ''', (self.id, current_date, session_type, current_time.strftime("%I:%M:%S %p")))
            print(f"Time-in recorded for {session_type} session.")

        conn.commit()
        conn.close()

    def get_faculty_name(self, faculty_id):
        """
        Fetches the faculty name from the database using the faculty ID.
        """
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()
        cursor.execute('''
            SELECT name FROM faculty WHERE id = ?
        ''', (faculty_id,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else "Unknown"

    def update_current_session(self, faculty_id):
        """
        Updates the current_session table with the detected faculty information
        to be used for real-time display in the UI.
        """
        try:
            conn = sqlite3.connect('database_new.db')
            cursor = conn.cursor()

            # Create the table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS current_session (
                    faculty_id TEXT PRIMARY KEY,
                    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'active'
                )
            ''')

            # Update or insert the current session record
            cursor.execute('''
                INSERT OR REPLACE INTO current_session (faculty_id, last_seen, status)
                VALUES (?, datetime('now', 'localtime'), 'active')
            ''', (faculty_id,))

            conn.commit()
            conn.close()
            print(f"Updated current session for faculty: {faculty_id}")
        except Exception as e:
            print(f"Error updating current session: {e}")

    def load_encodings(self):
        try:
            if os.path.exists('encodFile.p'):
                print("Loading Encode File ...")
                with open('encodFile.p', 'rb') as file:
                    encodeListKnownWithIds = pickle.load(file)

                # Validate the encoding structure
                if isinstance(encodeListKnownWithIds, tuple) and len(encodeListKnownWithIds) == 2:
                    encodings, ids = encodeListKnownWithIds

                    if len(encodings) > 0 and len(ids) > 0 and len(encodings) == len(ids):
                        self.encodeListKnown = encodings
                        self.teacherIds = ids
                        print(f"Encode File Loaded with {len(encodings)} encodings")
                    else:
                        print("Warning: Invalid encoding format (empty or mismatched lengths)")
                else:
                    print("Warning: Invalid encoding structure (not a tuple or wrong length)")
            else:
                print("No encodings file found")

                # Create a placeholder encoding
                print("Creating a placeholder encoding...")
                dummy_encoding = np.random.rand(128).astype(np.float64)
                self.encodeListKnown = [dummy_encoding]
                self.teacherIds = ["1"]

                # Save this placeholder
                encodeListKnownWithIds = (self.encodeListKnown, self.teacherIds)
                with open("encodFile.p", 'wb') as file:
                    pickle.dump(encodeListKnownWithIds, file)
                print("Created placeholder encoding")

        except Exception as e:
            print(f"Error loading encodings: {e}")
            # Create an empty encoding list as fallback
            self.encodeListKnown = []
            self.teacherIds = []

# Initialize the database for faculty and encodings
def init_faculty_db():
    """
    Initializes the SQLite database for storing faculty data.
    """
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS faculty (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL
        )
    ''')
    conn.commit()
    conn.close()

def init_encoding_db():
    """
    Initializes the SQLite database for storing faculty image encodings.
    """
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS encodings (
            faculty_id TEXT PRIMARY KEY,
            encoding BLOB NOT NULL,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')
    conn.commit()
    conn.close()

# Initialize the databases
init_faculty_db()
init_encoding_db()

import sqlite3

def add_faculty(id, name, department):
    """
    Adds a new faculty member to the SQLite database.
    """
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO faculty (id, name, department)
        VALUES (?, ?, ?)
    ''', (id, name, department))
    conn.commit()
    conn.close()
    print(f"Faculty {name} added successfully.")

# Example usage
# add_faculty('faculty_1', 'John Doe', 'Computer Science')

import cv2
import face_recognition
import pickle
import os
import sqlite3

def save_encoding(faculty_id, encoding):
    """
    Saves the face encoding for a faculty member in the SQLite database.
    """
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO encodings (faculty_id, encoding)
        VALUES (?, ?)
    ''', (faculty_id, pickle.dumps(encoding)))
    conn.commit()
    conn.close()
    print(f"Encoding for faculty ID {faculty_id} saved successfully.")

def generate_encodings(image_directory):
    """
    Generates face encodings for all images in the specified directory.
    """
    for filename in os.listdir(image_directory):
        if filename.endswith('.jpg') or filename.endswith('.png'):
            path = os.path.join(image_directory, filename)
            img = cv2.imread(path)
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Detect and encode faces
            encodings = face_recognition.face_encodings(img_rgb)
            if encodings:
                faculty_id = os.path.splitext(filename)[0]  # Use filename (without extension) as faculty ID
                save_encoding(faculty_id, encodings[0])
            else:
                print(f"No face detected in {filename}.")

# Example usage
# generate_encodings('path_to_image_directory')

# importing student images
folderPath = 'Images'
if not os.path.exists(folderPath):
    os.makedirs(folderPath)

# Get list of subdirectories in Images folder
pathList = os.listdir(folderPath)
print(pathList)

imgList = []
facultyIds = []
for faculty_dir in pathList:
    profile_path = os.path.join(folderPath, faculty_dir, 'profile.jpg')
    if os.path.exists(profile_path):
        img = cv2.imread(profile_path)
        if img is not None:
            imgList.append(img)
            facultyIds.append(faculty_dir)
            print(f"Loaded image for faculty: {faculty_dir}")
        else:
            print(f"Failed to load image: {profile_path}")

print(facultyIds)

def findEncodings(imagesList):
    encodeList = []
    for img in imagesList:
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        try:
            # Try to detect and encode face
            face_locations = face_recognition.face_locations(img)
            if len(face_locations) > 0:
                encode = face_recognition.face_encodings(img, face_locations)[0]
                encodeList.append(encode)
            else:
                print(f"Warning: No face detected in one of the faculty images")
        except Exception as e:
            print(f"Error processing faculty image: {str(e)}")
            continue
    return encodeList

print("Encodings ...")
encodeListKnown = findEncodings(imgList)
encodeListKnownWithIds = (encodeListKnown, facultyIds)
print("Encodings Complete")

file = open("encodFile.p",'wb')
pickle.dump(encodeListKnownWithIds,file)
file.close()
print("File saved")