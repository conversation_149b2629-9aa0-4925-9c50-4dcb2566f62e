/* Global Styles */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #45a049;
    --background-color: #f5f5f5;
    --text-color: #333;
    --border-radius: 8px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Navigation */
.navbar {
    background-color: white !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.4rem;
}

/* Main Content */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.video-container {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    padding-top: 0;
    margin-bottom: 0;
}

.video-container img {
    display: block;
    width: 100%;
    height: auto;
    object-fit: cover;
}

.info-container {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

/* Attendance Table */
.table-container {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Status Indicators */
.status-active {
    color: var(--primary-color);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .video-container, .info-container {
        padding: 15px;
    }
}

/* Current Faculty Card */
#current-faculty {
    min-height: 120px;
}

#faculty-info h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

/* Face Recognition Page */
.face-recognition-container {
    max-width: 900px;
    margin: 0 auto;
}
