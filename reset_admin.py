import sqlite3
from werkzeug.security import generate_password_hash
import os

# Delete existing database if it exists
if os.path.exists('database_new.db'):
    print("Deleting existing database_new.db")
    os.remove('database_new.db')

# Create a new database
conn = sqlite3.connect('database_new.db')
cursor = conn.cursor()

# Create tables
print("Creating tables...")
cursor.execute('''
    CREATE TABLE IF NOT EXISTS faculty (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        department TEXT NOT NULL,
        position TEXT,
        contact TEXT
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        faculty_id TEXT NOT NULL,
        date TEXT NOT NULL,
        session TEXT NOT NULL,
        time_in TEXT,
        time_out TEXT,
        FOREIGN KEY (faculty_id) REFERENCES faculty (id)
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS current_session (
        faculty_id TEXT PRIMARY KEY,
        last_seen TIMESTAMP,
        status TEXT
    )
''')

cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        role TEXT NOT NULL CHECK(role IN ('admin', 'faculty')),
        faculty_id TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP,
        FOREIGN KEY (faculty_id) REFERENCES faculty (id)
    )
''')

# Create admin user
print("Creating admin user...")
admin_username = 'Frass Admin'
admin_password = 'cictfrassadmin00'
hashed_password = generate_password_hash(admin_password)

cursor.execute('''
    INSERT INTO users (username, password, role, created_at)
    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
''', (admin_username, hashed_password, 'admin'))

conn.commit()
print("Admin user created successfully")

# Verify the admin user was created
cursor.execute('SELECT id, username, role FROM users WHERE role = "admin"')
admin_user = cursor.fetchone()
if admin_user:
    print(f"Admin user found: ID={admin_user[0]}, Username={admin_user[1]}, Role={admin_user[2]}")
else:
    print("Admin user not found in database! Something went wrong.")

conn.close()
print("Database initialization complete") 