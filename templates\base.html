<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Attendance System</title>
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='styles.css') }}" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        main {
            flex: 1;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 15px 0;
            margin-top: auto;
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">FRASS</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if not current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="/"><i class="fas fa-video"></i> Camera</a>
                        </li>
                    {% endif %}
                    
                    {% if current_user.is_authenticated %}
                        {% if current_user.is_admin() %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('admin_dashboard') }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('view_faculty') }}"><i class="fas fa-users"></i> Faculty</a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('faculty_dashboard') }}"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                            </li>
                        {% endif %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('attendance') }}"><i class="fas fa-clock"></i> Attendance</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link btn btn-primary text-white px-3 mx-2" href="{{ url_for('login') }}"><i class="fas fa-sign-in-alt"></i> Login</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <main>
        {% block content %}{% endblock %}
    </main>

    <footer class="footer">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 Face Recognition Attendance System</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
