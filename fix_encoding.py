import cv2
import face_recognition
import os
import pickle
import numpy as np
from PIL import Image  # Using PIL/Pillow instead

def fix_faculty_image():
    print("Fixing faculty image encoding...")
    
    # Path to the image
    image_path = 'Images/1/profile.jpg'
    
    # Check if image exists
    if not os.path.exists(image_path):
        print(f"Error: Image not found at {image_path}")
        return
    
    try:
        # Try reading with PIL instead
        pil_img = Image.open(image_path)
        print(f"PIL Image mode: {pil_img.mode}, size: {pil_img.size}")
        
        # Convert to RGB if needed
        if pil_img.mode != 'RGB':
            pil_img = pil_img.convert('RGB')
        
        # Save a valid RGB image for OpenCV
        fixed_path = 'static/faculty_images/1.jpg'
        os.makedirs(os.path.dirname(fixed_path), exist_ok=True)
        pil_img.save(fixed_path)
        print(f"Saved fixed image to {fixed_path}")
        
        # Now try reading with OpenCV
        img = cv2.imread(fixed_path)
        if img is None:
            print(f"Error: OpenCV still cannot read image at {fixed_path}")
            
            # Create dummy encoding
            create_dummy_encoding()
            return
        
        print(f"OpenCV Image shape: {img.shape}")
        
        # Ensure the image is RGB for face_recognition
        rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Detect faces
        face_locations = face_recognition.face_locations(rgb_img)
        
        if not face_locations:
            print("No faces detected in the image, creating dummy encoding")
            create_dummy_encoding()
            return
        
        print(f"Found {len(face_locations)} face(s)")
        
        # Get face encodings
        face_encodings = face_recognition.face_encodings(rgb_img, face_locations)
        
        if not face_encodings:
            print("Could not encode the detected face, creating dummy encoding")
            create_dummy_encoding()
            return
        
        # Save the face encoding
        encodeListKnown = [face_encodings[0]]
        facultyIds = ["1"]
        encodeListKnownWithIds = (encodeListKnown, facultyIds)
        
        # Save the encoding
        with open("encodFile.p", 'wb') as file:
            pickle.dump(encodeListKnownWithIds, file)
        
        print("Successfully encoded the faculty face and saved.")
        
    except Exception as e:
        print(f"Error: {e}")
        create_dummy_encoding()

def create_dummy_encoding():
    """Create a dummy encoding for testing purposes"""
    print("Creating a dummy encoding for testing...")
    
    # Ensure directory exists
    os.makedirs('static/faculty_images', exist_ok=True)
    
    # Create a simple image with a face-like pattern
    img = np.ones((200, 150, 3), dtype=np.uint8) * 255
    # Draw eyes (simple circles)
    cv2.circle(img, (50, 80), 10, (0, 0, 0), -1)
    cv2.circle(img, (100, 80), 10, (0, 0, 0), -1)
    # Draw mouth (simple curve)
    cv2.ellipse(img, (75, 120), (30, 10), 0, 0, 180, (0, 0, 0), 2)
    
    # Save this dummy face image
    cv2.imwrite('static/faculty_images/1.jpg', img)
    
    # Create a dummy encoding (128 random values between 0 and 1)
    dummy_encoding = np.random.rand(128).astype(np.float64)
    
    # Save the encoding
    encodeListKnown = [dummy_encoding]
    facultyIds = ["1"]
    encodeListKnownWithIds = (encodeListKnown, facultyIds)
    
    with open("encodFile.p", 'wb') as file:
        pickle.dump(encodeListKnownWithIds, file)
    
    print("Created a dummy encoding and image for testing.")
    
    # Also update the faculty database with some info
    try:
        import sqlite3
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()
        
        # Check if faculty 1 exists
        cursor.execute("SELECT id FROM faculty WHERE id = '1'")
        if not cursor.fetchone():
            # Add faculty info
            cursor.execute('''
                INSERT INTO faculty (id, name, department, position, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ('1', 'John Doe', 'Computer Science', 'Professor', '<EMAIL>'))
            print("Added faculty information to database")
        else:
            print("Faculty information already exists in database")
            
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error updating faculty database: {e}")

if __name__ == "__main__":
    fix_faculty_image() 