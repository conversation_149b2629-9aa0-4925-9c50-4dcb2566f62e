import face_recognition
import os
import cv2

def check_faculty_images():
    image_folder = "Images"
    images = os.listdir(image_folder)
    
    print("\n=== Checking Faculty Images ===\n")
    
    for img_name in images:
        print(f"\nChecking image: {img_name}")
        img_path = os.path.join(image_folder, img_name)
        
        # Read image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error: Could not read image {img_name}")
            continue
            
        # Convert from BGR to RGB
        rgb_img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Try to detect faces
        face_locations = face_recognition.face_locations(rgb_img)
        
        if len(face_locations) == 0:
            print(f"WARNING: No face detected in {img_name}")
        else:
            print(f"Success: Found {len(face_locations)} face(s) in {img_name}")
            
        # Print image dimensions
        print(f"Image dimensions: {img.shape}")

if __name__ == "__main__":
    check_faculty_images() 