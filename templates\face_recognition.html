{% extends 'base.html' %}
{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/face_recognition.css') }}">
{% endblock %}

{% block content %}
<div class="face-recognition-container">
    <div class="logo-container">
        <div class="logo-text">FRASS</div>
        <div class="logo-subtext">Face Recognition Attendance System</div>
    </div>
    
    <div class="recognition-layout" id="recognitionLayout">
        <!-- Video feed container -->
        <div class="video-wrapper">
            <div class="video-container">
                <img src="{{ url_for('video') }}" class="video-feed" alt="Video Feed" id="videoFeed">
                <div class="scan-line"></div>
                <div class="scan-effect"></div>
                <div class="face-overlay"></div>
                <div class="corner top-left"></div>
                <div class="corner top-right"></div>
                <div class="corner bottom-left"></div>
                <div class="corner bottom-right"></div>
                <div class="recognition-feedback">Faculty recognized!</div>
            </div>
            <div class="status-indicator">
                <div class="status-circle"></div>
                <div class="status-text">Ready to scan</div>
            </div>
        </div>
        
        <!-- Faculty information panel -->
        <div class="faculty-info-panel hidden" id="facultyInfoPanel">
            <div class="faculty-card">
                <div class="faculty-header">
                    <h3>Faculty Information</h3>
                    <div class="status-badge" id="statusBadge">ACTIVE</div>
                </div>
                <div class="faculty-profile">
                    <div class="faculty-image-container">
                        <img id="facultyImage" src="{{ url_for('static', filename='default-profile.jpg') }}" alt="Faculty Image" class="faculty-image">
                    </div>
                    <div class="faculty-details">
                        <h2 id="facultyName">Loading...</h2>
                        <p><i class="fas fa-briefcase"></i> <span id="facultyPosition">Position: --</span></p>
                    </div>
                </div>
                <div class="attendance-info">
                    <div class="time-info">
                        <p><i class="fas fa-sign-in-alt"></i> <span id="timeIn">--:--</span></p>
                        <p><i class="fas fa-sign-out-alt"></i> <span id="timeOut">--:--</span></p>
                    </div>
                    <div class="session-info">
                        <p><i class="fas fa-calendar"></i> <span id="sessionType">No session</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="instruction-panel">
        <div class="instruction-item">
            <i class="fas fa-user-check"></i>
            <span>Position your face in the frame</span>
        </div>
        <div class="instruction-item">
            <i class="fas fa-smile"></i>
            <span>Keep a neutral expression</span>
        </div>
        <div class="instruction-item">
            <i class="fas fa-lightbulb"></i>
            <span>Ensure good lighting</span>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const recognitionLayout = document.getElementById('recognitionLayout');
    const statusCircle = document.querySelector('.status-circle');
    const statusText = document.querySelector('.status-text');
    const videoContainer = document.querySelector('.video-container');
    const facultyInfoPanel = document.getElementById('facultyInfoPanel');
    const facultyImage = document.getElementById('facultyImage');
    const facultyName = document.getElementById('facultyName');
    const facultyPosition = document.getElementById('facultyPosition');
    const timeIn = document.getElementById('timeIn');
    const timeOut = document.getElementById('timeOut');
    const sessionType = document.getElementById('sessionType');
    const statusBadge = document.getElementById('statusBadge');
    const videoFeed = document.getElementById('videoFeed');
    
    // Ensure proper initial state
    facultyInfoPanel.classList.add('hidden');
    
    // Add loaded class to video feed when it's ready
    videoFeed.addEventListener('load', function() {
        this.classList.add('loaded');
    });
    
    // Track recognition state to avoid unnecessary UI updates
    let isCurrentlyRecognized = false;
    let lastFacultyId = null;
    let lastCheckTime = 0;
    const MIN_CHECK_INTERVAL = 100;
    
    // Function to check for recognized faculty
    function checkForFaculty() {
        const now = Date.now();
        if (now - lastCheckTime < MIN_CHECK_INTERVAL) {
            return;
        }
        lastCheckTime = now;
        
        fetch('/get_current_faculty')
            .then(response => response.json())
            .then(data => {
                const isFaceRecognized = data.faculty_id !== null && data.faculty_id !== undefined;
                
                if (isFaceRecognized !== isCurrentlyRecognized || 
                    (isFaceRecognized && lastFacultyId !== data.faculty_id)) {
                    
                    isCurrentlyRecognized = isFaceRecognized;
                    
                    if (isFaceRecognized) {
                        lastFacultyId = data.faculty_id;
                        
                        // Show faculty is recognized
                        recognitionLayout.classList.add('active');
                        facultyInfoPanel.classList.remove('hidden');
                        videoContainer.classList.add('recognizing');
                        statusCircle.classList.add('active');
                        statusText.textContent = data.multiple_detected ? 
                            'Multiple Faculty Detected!' : 'Faculty Recognized!';
                        
                        // Update faculty information
                        facultyName.textContent = data.name || 'Unknown';
                        facultyPosition.textContent = 'Position: ' + (data.position || '--');
                        
                        // Update faculty image
                        if (data.faculty_id) {
                            const imgUrl = "{{ url_for('faculty_image', faculty_id='PLACEHOLDER') }}".replace('PLACEHOLDER', data.faculty_id) + '?t=' + new Date().getTime();
                            facultyImage.onerror = function() {
                                this.src = "{{ url_for('static', filename='default-profile.jpg') }}";
                            };
                            facultyImage.src = imgUrl;
                        }
                        
                        // Update attendance status
                        if (data.attendance_status) {
                            timeIn.textContent = data.attendance_status.time_in || '--:--';
                            timeOut.textContent = data.attendance_status.time_out || '--:--';
                            sessionType.textContent = data.attendance_status.session ? 
                                new Date().toLocaleDateString('en-US', { 
                                    year: 'numeric', 
                                    month: 'long', 
                                    day: 'numeric' 
                                }) : 'No session';
                        }
                        
                        statusBadge.textContent = 'ACTIVE';
                        statusBadge.className = 'status-badge active';
                    } else {
                        lastFacultyId = null;
                        
                        // No faculty recognized
                        recognitionLayout.classList.remove('active');
                        facultyInfoPanel.classList.add('hidden');
                        videoContainer.classList.remove('recognizing');
                        statusCircle.classList.remove('active');
                        statusText.textContent = 'Ready to scan';
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching faculty data:', error);
                isCurrentlyRecognized = false;
                lastFacultyId = null;
                facultyInfoPanel.classList.add('hidden');
                recognitionLayout.classList.remove('active');
            });
    }
    
    // Check for faculty more frequently
    const checkInterval = setInterval(checkForFaculty, 100);
    
    // Initial check
    checkForFaculty();
    
    // Cleanup function
    window.addEventListener('beforeunload', function() {
        clearInterval(checkInterval);
    });
});
</script>
{% endblock %} 