<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Time Record - Catanduanes State University</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        
        body {
            font-family: Times New Roman, serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            width: 210mm; /* A4 width */
            min-height: 297mm; /* A4 height */
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            max-height: 297mm;
            display: flex;
            position: relative;
            page-break-after: avoid;
            page-break-inside: avoid;
            overflow: hidden;
        }
        
        .form-container {
            width: 105mm; /* Half of A4 width */
            height: 297mm; /* Full A4 height */
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .form-container:first-child {
            border-right: 1px dashed #000;
        }
        
        .container {
            width: 95%;
            margin: 10px auto;
            font-size: 9pt;
            display: flex;
            flex-direction: column;
            height: calc(100% - 20px); /* Full height minus margins */
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .logo {
            width: 50px;
            height: 50px;
            margin-right: 10px;
        }
        
        .header-text {
            flex: 1;
        }
        
        .header-text h1 {
            font-size: 12pt;
            margin: 0;
            font-weight: bold;
        }
        
        .header-text p {
            margin: 0;
            font-style: italic;
            font-size: 9pt;
        }
        
        .divider {
            border-bottom: 1px solid #000;
            margin: 5px 0;
        }
        
        .form-number {
            padding: 2px 0;
        }
        
        .title {
            text-align: center;
            font-size: 14pt;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .form-fields {
            margin-bottom: 10px;
        }
        
        .form-field {
            margin-bottom: 5px;
        }
        
        .form-field label {
            display: inline-block;
            width: 100px;
            font-weight: normal;
        }
        
        .form-field .line {
            display: inline-block;
            width: calc(100% - 105px);
            border-bottom: 1px solid #000;
        }
        
        .exception {
            margin: 5px 0;
            font-size: 9pt;
            text-align: right;
        }
        
        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 5px 0;
            font-size: 8pt;
            flex: 1;
            table-layout: fixed;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            height: 14px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        th {
            font-weight: bold;
        }
        
        .day-column {
            width: 20px;
        }
        
        .day-header {
            width: 20px;
            text-align: center;
        }
        
        .day-header div {
            writing-mode: vertical-lr;
            transform: rotate(180deg);
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .am-pm-header {
            text-align: center;
        }
        
        .arrival-departure {
            font-size: 7pt;
        }
        
        .total-row {
            font-weight: bold;
        }
        
        .footer-section {
            margin-top: 1px;
            margin-bottom: 5px;
        }
        
        .certification {
            margin: 5px 0;
            text-align: justify;
            font-size: 9pt;
        }
        
        .signature-container {
            text-align: right;
            margin: 15px 10px 5px 0;
        }
        
        .signature {
            margin-top: 25px;
            display: inline-block;
            border-top: 1px solid #000;
            padding-top: 2px;
            width: 200px;
            text-align: center;
        }
        
        .verification {
            margin: 5px 0;
            font-size: 9pt;
        }
        
        .verifier-container {
            text-align: right;
            margin-top: 35px;
            margin-right: 10px;
        }
        
        .verifier {
            font-weight: bold;
            text-decoration: underline;
            text-align: right;
            display: inline-block;
            width: 250px;
        }
        
        .verifier-title {
            text-align: center;
            display: inline-block;
            width: 200px;
            font-weight: bold;
            text-decoration: none;
        }
        
        @media print {
            body {
                width: 210mm;
                min-height: 297mm;
                max-height: 297mm;
                overflow: hidden;
            }
            
            .page {
                page-break-after: avoid;
                page-break-inside: avoid;
                overflow: hidden;
            }
            
            .content-wrapper {
                flex: none;
                display: block;
                height: auto;
            }
            
            table {
                margin-bottom: 10px;
                flex: none;
                display: table;
                height: auto;
            }
            
            .footer-section {
                position: static;
                margin-top: 10px;
                page-break-inside: avoid;
            }
            
            .certification, .verification {
                margin: 5px 0;
            }
        }
        
        .action-buttons {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="action-buttons">
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Print DTR
        </button>
        <button onclick="window.history.back()" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back
        </button>
    </div>
    
    <div class="page">
        <!-- Left copy of the form -->
        <div class="form-container">
            <div class="container">
                <div class="header">
                    <img src="{{ url_for('static', filename='CSU_Logo.png') }}" alt="Catanduanes State University Logo" class="logo" onerror="this.src='/static/CSU_Logo.png'">
                    <div class="header-text">
                        <p>Republic of the Philippines</p>
                        <h1>CATANDUANES STATE UNIVERSITY</h1>
                        <p>Virac, Catanduanes</p>
                    </div>
                </div>
                
                <div class="divider"></div>
                
                <div class="form-number">C.S. Form 48</div>
                
                <div class="title">DAILY TIME RECORD</div>
                
                <div class="form-fields">
                    <div class="form-field">
                        <label>Name:</label>
                        <span class="line">{{ faculty.name }}</span>
                    </div>
                    <div class="form-field">
                        <label>For the month of:</label>
                        <span class="line">{{ faculty.month_year }}</span>
                    </div>
                    <div class="form-field">
                        <label>Office Hours:</label>
                        <span class="line"></span>
                    </div>
                    <div class="form-field">
                        <span class="line"></span>
                    </div>
                </div>
                
                <div class="exception">
                    except Saturdays, Sundays and Holidays when required to render service.
                </div>
                
                <div class="content-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th rowspan="3" class="day-header">
                                    <div>DAY</div>
                                </th>
                                <th colspan="2">A.M.</th>
                                <th colspan="2">P.M.</th>
                                <th rowspan="2">OVER-<br>TIME</th>
                                <th colspan="3">TARDINESS/<br>UNDERTIME</th>
                            </tr>
                            <tr>
                                <th class="arrival-departure">ARRI-<br>VAL</th>
                                <th class="arrival-departure">DEPAR-<br>TURE</th>
                                <th class="arrival-departure">ARRI-<br>VAL</th>
                                <th class="arrival-departure">DEPAR-<br>TURE</th>
                                <th>DAY</th>
                                <th>HRS</th>
                                <th>MIN</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day in range(1, days_in_month + 1) %}
                            <tr>
                                <td>{{ day }}</td>
                                <td>{{ attendance[day].am_in }}</td>
                                <td>{{ attendance[day].am_out }}</td>
                                <td>{{ attendance[day].pm_in }}</td>
                                <td>{{ attendance[day].pm_out }}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            {% endfor %}
                            <tr>
                                <td colspan="5" style="text-align: center; font-weight: bold;">TOTAL</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="footer-section">
                    <div class="certification">
                        I HEREBY CERTIFY on my honor that the above is a true and correct
                        report of the hours of work performed, record of which was made daily
                        at the time of arrival at and departure from office.
                    </div>
                    
                    <div class="signature-container">
                        <div class="signature">Signature</div>
                    </div>
                    
                    <div class="verification">
                        VERIFIED: As to prescribed Office Hours.
                    </div>
                    
                    <div class="verifier-container">
                        <div class="verifier">MARIA CONCEPCION S. VERA, DIT</div>
                        <div class="verifier-title">Dean, CICT</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right copy of the form -->
        <div class="form-container">
            <div class="container">
                <div class="header">
                    <img src="{{ url_for('static', filename='CSU_Logo.png') }}" alt="Catanduanes State University Logo" class="logo" onerror="this.src='/static/CSU_Logo.png'">
                    <div class="header-text">
                        <p>Republic of the Philippines</p>
                        <h1>CATANDUANES STATE UNIVERSITY</h1>
                        <p>Virac, Catanduanes</p>
                    </div>
                </div>
                
                <div class="divider"></div>
                
                <div class="form-number">C.S. Form 48</div>
                
                <div class="title">DAILY TIME RECORD</div>
                
                <div class="form-fields">
                    <div class="form-field">
                        <label>Name:</label>
                        <span class="line">{{ faculty.name }}</span>
                    </div>
                    <div class="form-field">
                        <label>For the month of:</label>
                        <span class="line">{{ faculty.month_year }}</span>
                    </div>
                    <div class="form-field">
                        <label>Office Hours:</label>
                        <span class="line"></span>
                    </div>
                    <div class="form-field">
                        <span class="line"></span>
                    </div>
                </div>
                
                <div class="exception">
                    except Saturdays, Sundays and Holidays when required to render service.
                </div>
                
                <div class="content-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th rowspan="3" class="day-header">
                                    <div>DAY</div>
                                </th>
                                <th colspan="2">A.M.</th>
                                <th colspan="2">P.M.</th>
                                <th rowspan="2">OVER-<br>TIME</th>
                                <th colspan="3">TARDINESS/<br>UNDERTIME</th>
                            </tr>
                            <tr>
                                <th class="arrival-departure">ARRI-<br>VAL</th>
                                <th class="arrival-departure">DEPAR-<br>TURE</th>
                                <th class="arrival-departure">ARRI-<br>VAL</th>
                                <th class="arrival-departure">DEPAR-<br>TURE</th>
                                <th>DAY</th>
                                <th>HRS</th>
                                <th>MIN</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for day in range(1, days_in_month + 1) %}
                            <tr>
                                <td>{{ day }}</td>
                                <td>{{ attendance[day].am_in }}</td>
                                <td>{{ attendance[day].am_out }}</td>
                                <td>{{ attendance[day].pm_in }}</td>
                                <td>{{ attendance[day].pm_out }}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            {% endfor %}
                            <tr>
                                <td colspan="5" style="text-align: center; font-weight: bold;">TOTAL</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="footer-section">
                    <div class="certification">
                        I HEREBY CERTIFY on my honor that the above is a true and correct
                        report of the hours of work performed, record of which was made daily
                        at the time of arrival at and departure from office.
                    </div>
                    
                    <div class="signature-container">
                        <div class="signature">Signature</div>
                    </div>
                    
                    <div class="verification">
                        VERIFIED: As to prescribed Office Hours.
                    </div>
                    
                    <div class="verifier-container">
                        <div class="verifier">MARIA CONCEPCION S. VERA, DIT</div>
                        <div class="verifier-title">Dean, CICT</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include Bootstrap and Font Awesome for the buttons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</body>
</html>
