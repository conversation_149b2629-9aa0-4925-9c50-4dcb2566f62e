import sqlite3
from datetime import datetime

# Sample data for each teacher to generate DTR
data = {
    "00001": {
        "name": "<PERSON><PERSON> <PERSON>",
        "DTR": {
            "2024-09-01": {
                "time_in_morning": "08:00:00",
                "time_out_morning": "12:00:00",
                "time_in_afternoon": "13:00:00",
                "time_out_afternoon": "17:00:00"
            }
        }
    },
    "00002": {
        "name": "Prof<PERSON>",
        "DTR": {
            "2024-09-01": {
                "time_in_morning": "08:05:00",
                "time_out_morning": "12:15:00",
                "time_in_afternoon": "13:10:00",
                "time_out_afternoon": "17:10:00"
            },
            "2025-02-02": {
                "time_in_morning": "08:05:00",
                "time_out_morning": "12:15:00",
                "time_in_afternoon": "13:10:00",
                "time_out_afternoon": "16:10:00"
            }
        }
    },
    "00003": {
        "name": "<PERSON><PERSON> <PERSON>",
        "DTR": {
            "2024-09-01": {
                "time_in_morning": "08:05:00",
                "time_out_morning": "12:15:00",
                "time_in_afternoon": "13:10:00",
                "time_out_afternoon": "17:10:00"
            },
            "2024-09-02": {
                "time_in_morning": "08:05:00",
                "time_out_morning": "12:15:00",
                "time_in_afternoon": "13:20:00",
                "time_out_afternoon": "18:10:00"
            }
        }
    },
    # Add more teachers with sample DTR data
}

def calculate_total_hours(time_in_morning, time_out_morning, time_in_afternoon, time_out_afternoon):
    """
    Calculates total hours based on time-in and time-out records.
    """
    format = "%H:%M:%S"
    morning_hours = 0
    afternoon_hours = 0

    try:
        # Calculate morning hours if time_in and time_out are available
        if time_in_morning and time_out_morning:
            time_in_m = datetime.strptime(time_in_morning, format)
            time_out_m = datetime.strptime(time_out_morning, format)
            morning_hours = (time_out_m - time_in_m).seconds / 3600  # Convert to hours

        # Calculate afternoon hours if time_in and time_out are available
        if time_in_afternoon and time_out_afternoon:
            time_in_a = datetime.strptime(time_in_afternoon, format)
            time_out_a = datetime.strptime(time_out_afternoon, format)
            afternoon_hours = (time_out_a - time_in_a).seconds / 3600
    except ValueError as e:
        print(f"Error calculating total hours: {e}")
        return 0

    total_hours = morning_hours + afternoon_hours
    return round(total_hours, 2)  # Return total hours rounded to 2 decimal places

def import_dtr_data():
    """
    Imports teacher data and DTR records into the SQLite database.
    """
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()

    # Ensure the faculty table exists
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS faculty (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT
        )
    ''')

    # Ensure the attendance table exists
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            faculty_id TEXT NOT NULL,
            date TEXT NOT NULL,
            session TEXT NOT NULL,
            time_in TEXT,
            time_out TEXT,
            total_hours REAL,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')

    # Insert teacher data and DTR records
    for faculty_id, teacher_data in data.items():
        # Insert or update faculty data
        cursor.execute('''
            INSERT OR IGNORE INTO faculty (id, name, department)
            VALUES (?, ?, ?)
        ''', (faculty_id, teacher_data["name"], None))

        # Insert DTR records
        for date, dtr_info in teacher_data["DTR"].items():
            # Calculate total hours for the day
            total_hours = calculate_total_hours(
                dtr_info.get("time_in_morning"),
                dtr_info.get("time_out_morning"),
                dtr_info.get("time_in_afternoon"),
                dtr_info.get("time_out_afternoon")
            )

            # Insert morning session
            if dtr_info.get("time_in_morning") and dtr_info.get("time_out_morning"):
                cursor.execute('''
                    INSERT INTO attendance (faculty_id, date, session, time_in, time_out, total_hours)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (faculty_id, date, 'morning', dtr_info["time_in_morning"], dtr_info["time_out_morning"], total_hours))

            # Insert afternoon session
            if dtr_info.get("time_in_afternoon") and dtr_info.get("time_out_afternoon"):
                cursor.execute('''
                    INSERT INTO attendance (faculty_id, date, session, time_in, time_out, total_hours)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (faculty_id, date, 'afternoon', dtr_info["time_in_afternoon"], dtr_info["time_out_afternoon"], total_hours))

    conn.commit()
    conn.close()
    print("DTR data imported successfully!")

# Run the import function
import_dtr_data()
