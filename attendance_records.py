import sqlite3
from datetime import datetime

def init_attendance_db():
    """
    Initializes the SQLite database for storing attendance records.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            faculty_id TEXT NOT NULL,
            date TEXT NOT NULL,
            session TEXT NOT NULL,
            time_in TEXT,
            time_out TEXT,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')
    conn.commit()
    conn.close()

def update_time_in_out(faculty_id):
    """
    Updates the time-in or time-out record for the detected faculty in SQLite.
    Only one time-in and time-out record per faculty per day.
    """
    current_time = datetime.now()
    current_date = current_time.strftime("%Y-%m-%d")
    current_time_str = current_time.strftime("%I:%M:%S %p")

    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    try:
        # Check if faculty already has a record for today
        cursor.execute('''
            SELECT time_in, time_out 
            FROM attendance 
            WHERE faculty_id = ? AND date = ?
        ''', (faculty_id, current_date))
        record = cursor.fetchone()

        if record:
            time_in, time_out = record
            # If there's no time_out yet, update it
            if not time_out:
                # Only update time_out if at least 1 minute has passed since time_in
                time_in_dt = datetime.strptime(time_in, "%I:%M:%S %p")
                current_dt = datetime.strptime(current_time_str, "%I:%M:%S %p")
                time_diff = (current_dt - time_in_dt).total_seconds() / 60

                if time_diff >= 1:
                    cursor.execute('''
                        UPDATE attendance 
                        SET time_out = ? 
                        WHERE faculty_id = ? AND date = ? AND time_out IS NULL
                    ''', (current_time_str, faculty_id, current_date))
                    print(f"Time-out recorded for {faculty_id} at {current_time_str}")
        else:
            # No record exists for today, create new time-in record
            cursor.execute('''
                INSERT INTO attendance (faculty_id, date, session, time_in)
                VALUES (?, ?, ?, ?)
            ''', (faculty_id, current_date, 'day', current_time_str))
            print(f"Time-in recorded for {faculty_id} at {current_time_str}")

        conn.commit()

    except Exception as e:
        print(f"Error updating attendance: {e}")
        conn.rollback()
    finally:
        conn.close()

def calculate_total_hours(time_in_morning, time_out_morning, time_in_afternoon, time_out_afternoon):
    """
    Calculates total hours based on time-in and time-out records.
    """
    format = "%I:%M:%S %p"
    morning_hours = 0
    afternoon_hours = 0

    try:
        # Calculate morning hours if time_in and time_out are available
        if time_in_morning and time_out_morning:
            time_in_m = datetime.strptime(time_in_morning, format)
            time_out_m = datetime.strptime(time_out_morning, format)
            morning_hours = (time_out_m - time_in_m).seconds / 3600  # Convert to hours

        # Calculate afternoon hours if time_in and time_out are available
        if time_in_afternoon and time_out_afternoon:
            time_in_a = datetime.strptime(time_in_afternoon, format)
            time_out_a = datetime.strptime(time_out_afternoon, format)
            afternoon_hours = (time_out_a - time_in_a).seconds / 3600
    except ValueError as e:
        print(f"Error calculating total hours: {e}")
        return 0

    total_hours = morning_hours + afternoon_hours
    return round(total_hours, 2)  # Return total hours rounded to 2 decimal places

def import_dtr_data(data):
    """
    Imports teacher data and DTR records into the SQLite database.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Insert teacher data and DTR records
    for faculty_id, teacher_data in data.items():
        # Insert or update faculty data
        cursor.execute('''
            INSERT OR IGNORE INTO faculty (id, name, department)
            VALUES (?, ?, ?)
        ''', (faculty_id, teacher_data["name"], None))

        # Insert DTR records
        for date, dtr_info in teacher_data["DTR"].items():
            # Insert morning session
            if dtr_info.get("time_in_morning") and dtr_info.get("time_out_morning"):
                cursor.execute('''
                    INSERT INTO attendance (faculty_id, date, session, time_in, time_out)
                    VALUES (?, ?, ?, ?, ?)
                ''', (faculty_id, date, 'morning', dtr_info["time_in_morning"], dtr_info["time_out_morning"]))

            # Insert afternoon session
            if dtr_info.get("time_in_afternoon") and dtr_info.get("time_out_afternoon"):
                cursor.execute('''
                    INSERT INTO attendance (faculty_id, date, session, time_in, time_out)
                    VALUES (?, ?, ?, ?, ?)
                ''', (faculty_id, date, 'afternoon', dtr_info["time_in_afternoon"], dtr_info["time_out_afternoon"]))

    conn.commit()
    conn.close()
    print("DTR data imported successfully!")

# Initialize the attendance database
init_attendance_db()
