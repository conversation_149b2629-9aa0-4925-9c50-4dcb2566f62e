#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to update faculty information with position and email data
for testing the enhanced face recognition display.
"""
import sqlite3
import os
from faculty_data import update_faculty, get_faculty_info

def update_all_faculty_details():
    """
    Update all faculty records with sample position and email details
    """
    # Connect to the database
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    
    # Get all faculty IDs
    cursor.execute('SELECT id, name FROM faculty')
    faculty_list = cursor.fetchall()
    conn.close()
    
    if not faculty_list:
        print("No faculty records found in the database.")
        return
    
    print(f"Found {len(faculty_list)} faculty records to update.")
    
    # Sample departments and positions
    departments = ["Computer Science", "Information Technology", "Mathematics", 
                   "Engineering", "Physics", "Business Administration"]
    positions = ["Professor", "Associate Professor", "Assistant Professor", 
                 "Instructor", "Lecturer", "Department Chair"]
    
    for idx, (faculty_id, name) in enumerate(faculty_list):
        # Generate sample data
        department = departments[idx % len(departments)]
        position = positions[idx % len(positions)]
        email = f"{name.lower().replace(' ', '.')}@university.edu"
        
        # Update faculty record
        update_faculty(
            id=faculty_id,
            department=department,
            position=position,
            email=email
        )
        
        # Verify update
        faculty_info = get_faculty_info(faculty_id)
        print(f"Updated {faculty_id}: {faculty_info['name']}, {faculty_info['position']}, {faculty_info['email']}")

def ensure_faculty_images():
    """
    Ensure faculty images are properly placed in static/faculty_images
    """
    # Source directory
    source_dir = 'Images'
    # Target directory
    target_dir = 'static/faculty_images'
    
    # Create target directory if it doesn't exist
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"Created directory: {target_dir}")
    
    # Check if Images directory exists and has subdirectories
    if os.path.exists(source_dir):
        faculty_dirs = [d for d in os.listdir(source_dir) if os.path.isdir(os.path.join(source_dir, d))]
        
        for faculty_dir in faculty_dirs:
            # Look for profile.jpg in each faculty directory
            profile_path = os.path.join(source_dir, faculty_dir, 'profile.jpg')
            if os.path.exists(profile_path):
                # Copy to target directory with faculty_id as filename
                target_path = os.path.join(target_dir, f"{faculty_dir}.jpg")
                try:
                    with open(profile_path, 'rb') as src_file, open(target_path, 'wb') as dst_file:
                        dst_file.write(src_file.read())
                    print(f"Copied image for {faculty_dir} to {target_path}")
                except Exception as e:
                    print(f"Error copying image for {faculty_dir}: {e}")
    else:
        print(f"Source directory {source_dir} not found.")

if __name__ == "__main__":
    print("Updating faculty information...")
    update_all_faculty_details()
    
    print("\nEnsuring faculty images are available...")
    ensure_faculty_images()
    
    print("\nUpdate completed!") 