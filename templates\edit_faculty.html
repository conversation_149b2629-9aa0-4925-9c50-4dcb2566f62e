{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-edit"></i> Edit Faculty Member</h5>
                </div>
                <div class="card-body">
                    {% if error %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}
                    
                    {% if success %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> {{ success }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}
                    
                    <form method="POST" action="{{ url_for('edit_faculty', faculty_id=faculty.id) }}" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Left Column - Faculty Information -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-user"></i> Faculty Information</h5>
                                <hr>
                                
                                <div class="mb-3">
                                    <label for="faculty_id" class="form-label">Faculty ID</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                        <input type="text" class="form-control" id="faculty_id" value="{{ faculty.id }}" readonly>
                                    </div>
                                    <div class="form-text">Faculty ID cannot be changed</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="name" name="name" value="{{ faculty.name }}" required>
                                    </div>
                                </div>
                                
                                <!-- Department field removed -->
                                <input type="hidden" name="department" value="CICT">
                            </div>
                            
                            <!-- Right Column - Additional Information -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-info-circle"></i> Additional Information</h5>
                                <hr>
                                
                                <div class="mb-3">
                                    <label for="position" class="form-label">Position</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                        <select class="form-select" id="position" name="position">
                                            <option value="" {% if not faculty.position %}selected{% endif %}>Select position</option>
                                            <option value="Staff" {% if faculty.position == "Staff" %}selected{% endif %}>Staff</option>
                                            <option value="Faculty" {% if faculty.position == "Faculty" %}selected{% endif %}>Faculty</option>
                                            <option value="Admin" {% if faculty.position == "Admin" %}selected{% endif %}>Admin</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="contact" class="form-label">Contact Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="text" class="form-control" id="contact" name="contact" value="{{ faculty.contact }}">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="profile_image" class="form-label">Update Profile Image</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-image"></i></span>
                                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                                    </div>
                                    <div class="form-text">Leave empty to keep current image</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12 d-flex justify-content-between">
                                <a href="{{ url_for('view_faculty') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Faculty List
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 