{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="fas fa-clock"></i> Attendance Records</h2>
                <h5 class="text-muted">{{ faculty_name }}</h5>
            </div>
            <div>
                <a href="{{ url_for('view_faculty') }}" class="btn btn-primary me-2">
                    <i class="fas fa-arrow-left"></i> Back to Faculty List
                </a>
                <a href="{{ url_for('logout') }}" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-body">
            {% if records %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Time In</th>
                            <th>Time Out</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for record in records %}
                        <tr>
                            <td>{{ record[0] }}</td>
                            <td>{{ record[3] if record[3] else '-' }}</td>
                            <td>{{ record[4] if record[4] else '-' }}</td>
                            <td>{{ record[5] }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No attendance records found</h5>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 