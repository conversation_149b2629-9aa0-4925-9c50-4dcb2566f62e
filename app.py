import sqlite3
from datetime import datetime
from flask import Flask, render_template, Response, jsonify, request, redirect, url_for, send_file, flash
from camera import Video
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import os
import pickle
import csv
from io import StringIO
import calendar
from datetime import datetime, timedelta
import base64
import hashlib
import uuid
from werkzeug.utils import secure_filename
from PIL import Image
import time

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Replace with a secure secret key in production

# Add template filters
@app.template_filter('file_exists')
def file_exists(path):
    return os.path.exists(path)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Initialize SQLite database
def init_db():
    """Initialize the database with required tables"""
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Create faculty table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS faculty (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT NOT NULL,
            position TEXT,
            contact TEXT
        )
    ''')

    # Create attendance table if not exists
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            faculty_id TEXT NOT NULL,
            date TEXT NOT NULL,
            session TEXT NOT NULL,
            time_in TEXT,
            time_out TEXT,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')

    # Create current_session table to track active faculty
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS current_session (
            faculty_id TEXT PRIMARY KEY,
            last_seen TIMESTAMP,
            status TEXT
        )
    ''')

    # Create users table for authentication
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT NOT NULL CHECK(role IN ('admin', 'admin_user', 'faculty')),
            faculty_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
        )
    ''')

    # Update existing faculty table if needed
    try:
        cursor.execute('ALTER TABLE faculty ADD COLUMN position TEXT')
    except sqlite3.OperationalError:
        pass  # Column already exists

    try:
        cursor.execute('ALTER TABLE faculty ADD COLUMN contact TEXT')
    except sqlite3.OperationalError:
        pass  # Column already exists

    conn.commit()
    conn.close()

# User class for Flask-Login
class User(UserMixin):
    def __init__(self, id, username, role, faculty_id=None):
        self.id = id
        self.username = username
        self.role = role
        self.faculty_id = faculty_id

    def is_admin(self):
        return self.role == 'admin' or self.role == 'admin_user'

    def is_super_admin(self):
        return self.role == 'admin'

    def is_faculty(self):
        return self.role == 'faculty'

@login_manager.user_loader
def load_user(user_id):
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, role, faculty_id FROM users WHERE id = ?', (user_id,))
    user_data = cursor.fetchone()
    conn.close()

    if user_data:
        return User(user_data[0], user_data[1], user_data[2], user_data[3])
    return None

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            return redirect(url_for('login', error='Admin access required'))
        return f(*args, **kwargs)
    return decorated_function

def super_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_super_admin():
            return redirect(url_for('login', error='Super Admin access required'))
        return f(*args, **kwargs)
    return decorated_function

def faculty_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_faculty():
            return redirect(url_for('login', error='Faculty access required'))
        return f(*args, **kwargs)
    return decorated_function

def calculate_duration(time_in, time_out):
    """Calculate duration between time_in and time_out in hours and minutes"""
    if not time_in or not time_out:
        return "-"
    try:
        time_in = datetime.strptime(time_in, "%I:%M:%S %p")
        time_out = datetime.strptime(time_out, "%I:%M:%S %p")
        duration = time_out - time_in
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        return f"{hours}h {minutes}m"
    except Exception as e:
        print(f"Error calculating duration: {e}")
        return "-"

@app.route('/')
def index():
    if current_user.is_authenticated:
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard'))
        else:
            return redirect(url_for('faculty_dashboard'))
    return redirect(url_for('face_recognition_page'))

def gen(camera):
    """
    Generates frames for the video feed.
    """
    try:
        while True:
            frame = camera.get_frame()
            if frame is None:
                break
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n\r\n')
    finally:
        camera.release()

@app.route('/video')
def video():
    try:
        return Response(gen(Video()),
                       mimetype='multipart/x-mixed-replace; boundary=frame')
    except Exception as e:
        print(f"Error in video route: {e}")
        return "Video stream error", 500

@app.route('/registration_video')
@login_required
@admin_required
def registration_video():
    """Video feed specifically for faculty registration"""
    try:
        camera = Video()
        camera.registration_mode = True  # Set a flag to indicate we're in registration mode
        return Response(gen(camera),
                       mimetype='multipart/x-mixed-replace; boundary=frame')
    except Exception as e:
        print(f"Error in registration video route: {e}")
        return "Video stream error", 500

@app.route('/capture_registration_image')
@login_required
@admin_required
def capture_registration_image():
    """Captures an image from the video feed for registration - simplified version"""
    camera = None
    try:
        # Create and use camera in a single operation
        camera = Video()
        frame = camera.capture_frame()

        if frame is not None:
            # Convert to base64 and return
            image_data = base64.b64encode(frame).decode('utf-8')
            data_url = f'data:image/jpeg;base64,{image_data}'
            return jsonify({'success': True, 'image': data_url})
        else:
            # Simple error message
            return jsonify({
                'success': False,
                'error': 'Could not capture image. Please make sure your camera is connected and working.'
            })
    except Exception as e:
        print(f"Camera error: {e}")
        return jsonify({
            'success': False,
            'error': 'Camera error occurred. Please try again.'
        })
    finally:
        # Always release the camera
        if camera:
            camera.release()

@app.route('/get_current_faculty')
def get_current_faculty():
    """
    Returns information about the currently detected faculty member
    and today's attendance statistics.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get current faculty info with a 1-second timeout
    cursor.execute('''
        SELECT f.id, f.name, f.department, f.position, f.email, cs.last_seen
        FROM current_session cs
        JOIN faculty f ON cs.faculty_id = f.id
        WHERE cs.last_seen >= datetime('now', 'localtime', '-1 seconds')
        ORDER BY cs.last_seen DESC
    ''')
    faculty_list = cursor.fetchall()

    # Get today's attendance stats
    today = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT
            SUM(CASE WHEN time_in IS NOT NULL THEN 1 ELSE 0 END) as checkins,
            SUM(CASE WHEN time_out IS NOT NULL THEN 1 ELSE 0 END) as checkouts
        FROM attendance
        WHERE date = ?
    ''', (today,))
    stats = cursor.fetchone()

    # If multiple faculty are detected, use the most recently seen one
    faculty = faculty_list[0] if faculty_list else None

    # Get faculty's attendance status for today
    if faculty:
        cursor.execute('''
            SELECT time_in, time_out, session
            FROM attendance
            WHERE faculty_id = ? AND date = ?
            ORDER BY session DESC
        ''', (faculty[0], today))
        attendance = cursor.fetchone()

        # Check if faculty image exists
        image_path = os.path.join('static', 'faculty_images', f'{faculty[0]}.jpg')
        has_image = os.path.exists(image_path)
    else:
        attendance = None
        has_image = False

    conn.close()

    return jsonify({
        'faculty_id': faculty[0] if faculty else None,
        'name': faculty[1] if faculty else None,
        'position': faculty[3] if faculty and faculty[3] else None,
        'email': faculty[4] if faculty and faculty[4] else None,
        'last_seen': faculty[5] if faculty else None,
        'multiple_detected': len(faculty_list) > 1,
        'image_url': url_for('static', filename=f'faculty_images/{faculty[0]}.jpg') if faculty and has_image else url_for('static', filename='default-profile.jpg') if faculty else None,
        'attendance_status': {
            'time_in': attendance[0] if attendance else None,
            'time_out': attendance[1] if attendance else None,
            'session': attendance[2] if attendance else None
        } if faculty and attendance else None,
        'checkins': stats[0] if stats else 0,
        'checkouts': stats[1] if stats else 0
    })

@app.route('/attendance')
@login_required
def attendance():
    """
    Fetches and displays attendance records from the database.
    Shows one record per faculty per day.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # If user is faculty, only show their records
    if current_user.role == 'faculty':
        cursor.execute('''
            SELECT
                a.date,
                f.name,
                a.time_in,
                a.time_out
            FROM attendance a
            LEFT JOIN faculty f ON a.faculty_id = f.id
            WHERE a.faculty_id = ?
            ORDER BY a.date DESC, a.time_in DESC
        ''', (current_user.faculty_id,))
    else:
        # Admin can see all records
        cursor.execute('''
            SELECT
                a.date,
                f.name,
                a.time_in,
                a.time_out
            FROM attendance a
            LEFT JOIN faculty f ON a.faculty_id = f.id
            ORDER BY a.date DESC, a.time_in DESC
        ''')

    records = cursor.fetchall()
    conn.close()

    # Format records with duration
    formatted_records = []
    for record in records:
        formatted_record = list(record)
        formatted_record.append(calculate_duration(record[2], record[3]))
        formatted_records.append(formatted_record)

    return render_template('attendance.html', records=formatted_records)

def record_attendance(faculty_id, event_type, session):
    """
    Records attendance for a faculty member.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    today = datetime.now().strftime('%Y-%m-%d')
    current_time = datetime.now().strftime('%I:%M:%S %p')  # Use 12-hour format with AM/PM

    if event_type == 'in':
        cursor.execute('''
            INSERT INTO attendance (faculty_id, date, session, time_in)
            VALUES (?, ?, ?, ?)
        ''', (faculty_id, today, session, current_time))
    else:  # event_type == 'out'
        cursor.execute('''
            UPDATE attendance
            SET time_out = ?
            WHERE faculty_id = ? AND date = ? AND session = ? AND time_out IS NULL
        ''', (current_time, faculty_id, today, session))

    conn.commit()
    conn.close()

def update_current_session(faculty_id, status):
    """
    Updates the current session table with the latest faculty detection.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO current_session (faculty_id, last_seen, status)
        VALUES (?, datetime('now'), ?)
    ''', (faculty_id, status))
    conn.commit()
    conn.close()

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard'))
        else:
            return redirect(url_for('faculty_dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        try:
            conn = sqlite3.connect('database_new.db')
            cursor = conn.cursor()
            cursor.execute('SELECT id, username, password, role, faculty_id FROM users WHERE username = ?', (username,))
            user_data = cursor.fetchone()

            if user_data and check_password_hash(user_data[2], password):
                user = User(user_data[0], user_data[1], user_data[3], user_data[4])
                login_user(user)

                # Update last login timestamp
                cursor.execute('''
                    UPDATE users
                    SET last_login = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (user_data[0],))
                conn.commit()

                if user.is_admin():
                    return redirect(url_for('admin_dashboard'))
                else:
                    return redirect(url_for('faculty_dashboard'))
            else:
                return render_template('login.html', error='Invalid username or password')
        except Exception as e:
            print(f"Error during login: {e}")
            return render_template('login.html', error='An error occurred during login')
        finally:
            conn.close()

    return render_template('login.html')

@app.route('/get_current_stats')
def get_current_stats():
    """
    Returns today's attendance statistics
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    today = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT
            SUM(CASE WHEN time_in IS NOT NULL THEN 1 ELSE 0 END) as checkins,
            SUM(CASE WHEN time_out IS NOT NULL THEN 1 ELSE 0 END) as checkouts
        FROM attendance
        WHERE date = ?
    ''', (today,))
    stats = cursor.fetchone()
    conn.close()

    return jsonify({
        'checkins': stats[0] if stats[0] else 0,
        'checkouts': stats[1] if stats[1] else 0
    })

@app.route('/logout')
@login_required
def logout():
    """
    Log out the current user and redirect to the login page.
    """
    logout_user()
    return redirect(url_for('login'))

@app.route('/register', methods=['GET', 'POST'])
@login_required
@admin_required
def register():
    """
    Register a new faculty member with uploaded image.
    """
    if request.method == 'POST':
        try:
            # Get form data
            faculty_id = request.form.get('faculty_id')
            name = request.form.get('name')
            department = request.form.get('department')
            position = request.form.get('position')
            contact_number = request.form.get('contact_number')
            username = request.form.get('username')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')

            # Validate password
            if password != confirm_password:
                return render_template('register.html', error='Passwords do not match')

            if len(password) < 8:
                return render_template('register.html', error='Password must be at least 8 characters long')

            # Check if username already exists
            conn = sqlite3.connect('database_new.db')
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            existing_user = cursor.fetchone()

            if existing_user:
                conn.close()
                return render_template('register.html', error='Username already exists')

            # Check if faculty_id already exists
            cursor.execute('SELECT id FROM faculty WHERE id = ?', (faculty_id,))
            existing_faculty = cursor.fetchone()

            if existing_faculty:
                conn.close()
                return render_template('register.html', error='Faculty ID already exists')

            # Handle profile image upload
            profile_image = request.files.get('profile_image')
            if not profile_image:
                conn.close()
                return render_template('register.html', error='Profile image is required')

            # Save the profile image
            img_dir = os.path.join('Images', faculty_id)
            os.makedirs(img_dir, exist_ok=True)
            img_path = os.path.join(img_dir, 'profile.jpg')
            profile_image.save(img_path)

            # Copy image to static directory
            static_img_dir = os.path.join('static', 'faculty_images')
            os.makedirs(static_img_dir, exist_ok=True)
            static_img_path = os.path.join(static_img_dir, f'{faculty_id}.jpg')
            try:
                img = Image.open(img_path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(static_img_path, 'JPEG')
                print(f"Copied faculty image to static directory: {static_img_path}")
            except Exception as e:
                print(f"Error copying faculty image to static directory: {e}")

            # Insert faculty record
            cursor.execute('''
                INSERT INTO faculty (id, name, department, position, contact)
                VALUES (?, ?, ?, ?, ?)
            ''', (faculty_id, name, department, position, contact_number))

            # Hash the password
            hashed_password = generate_password_hash(password)

            # Insert user record
            role = 'faculty'
            # Determine role based on position
            if position == 'Admin':
                # All admins created through registration are regular admins
                role = 'admin_user'

            cursor.execute('''
                INSERT INTO users (username, password, role, faculty_id)
                VALUES (?, ?, ?, ?)
            ''', (username, hashed_password, role, faculty_id))

            conn.commit()

            # Generate facial encoding
            from EncodeGenerator import generate_encodings
            generate_encodings('Images')

            conn.close()

            return render_template('register.html', success=f'Successfully registered {name} as {role}')

        except Exception as e:
            print(f"Error in registration: {e}")
            return render_template('register.html', error=f'Registration error: {str(e)}')

    return render_template('register.html')

@app.route('/register-camera', methods=['GET', 'POST'])
@login_required
@admin_required
def register_camera():
    """
    Register a new faculty member with camera-captured image.
    """
    if request.method == 'POST':
        try:
            # Get form data
            faculty_id = request.form.get('faculty_id')
            name = request.form.get('name')
            department = request.form.get('department')
            position = request.form.get('position')
            contact_number = request.form.get('contact_number')
            username = request.form.get('username')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')
            captured_image = request.form.get('captured_image')

            # Validate captured image
            if not captured_image or not captured_image.startswith('data:image'):
                return render_template('register_camera.html', error='You must capture a photo')

            # Validate password
            if password != confirm_password:
                return render_template('register_camera.html', error='Passwords do not match')

            if len(password) < 8:
                return render_template('register_camera.html', error='Password must be at least 8 characters long')

            # Check if username already exists
            conn = sqlite3.connect('database_new.db')
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            existing_user = cursor.fetchone()

            if existing_user:
                conn.close()
                return render_template('register_camera.html', error='Username already exists')

            # Check if faculty_id already exists
            cursor.execute('SELECT id FROM faculty WHERE id = ?', (faculty_id,))
            existing_faculty = cursor.fetchone()

            if existing_faculty:
                conn.close()
                return render_template('register_camera.html', error='Faculty ID already exists')

            # Process and save the captured image
            import base64
            import io

            # Remove the data URL prefix to get the base64 string
            image_data = captured_image.split(',')[1]
            image_bytes = base64.b64decode(image_data)

            # Create image from bytes
            img = Image.open(io.BytesIO(image_bytes))

            # Save the image
            img_dir = os.path.join('Images', faculty_id)
            os.makedirs(img_dir, exist_ok=True)
            img_path = os.path.join(img_dir, 'profile.jpg')
            img.save(img_path, 'JPEG')

            # Copy image to static directory
            static_img_dir = os.path.join('static', 'faculty_images')
            os.makedirs(static_img_dir, exist_ok=True)
            static_img_path = os.path.join(static_img_dir, f'{faculty_id}.jpg')
            try:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(static_img_path, 'JPEG')
                print(f"Copied faculty image to static directory: {static_img_path}")
            except Exception as e:
                print(f"Error copying faculty image to static directory: {e}")

            # Insert faculty record
            cursor.execute('''
                INSERT INTO faculty (id, name, department, position, contact)
                VALUES (?, ?, ?, ?, ?)
            ''', (faculty_id, name, department, position, contact_number))

            # Hash the password
            hashed_password = generate_password_hash(password)

            # Insert user record
            role = 'faculty'
            # Determine role based on position
            if position == 'Admin':
                # All admins created through registration are regular admins
                role = 'admin_user'

            cursor.execute('''
                INSERT INTO users (username, password, role, faculty_id)
                VALUES (?, ?, ?, ?)
            ''', (username, hashed_password, role, faculty_id))

            conn.commit()

            # Generate facial encoding
            from EncodeGenerator import generate_encodings
            generate_encodings('Images')

            conn.close()

            return render_template('register_camera.html', success=f'Successfully registered {name} as {role}')

        except Exception as e:
            print(f"Error in registration: {e}")
            return render_template('register_camera.html', error=f'Registration error: {str(e)}')

    return render_template('register_camera.html')

@app.route('/admin/dashboard')
@login_required
@admin_required
def admin_dashboard():
    """
    Admin dashboard showing attendance statistics
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get total faculty count
    cursor.execute('SELECT COUNT(*) FROM faculty')
    faculty_count = cursor.fetchone()[0]

    # Get today's attendance stats
    today = datetime.now().strftime('%Y-%m-%d')
    cursor.execute('''
        SELECT
            COUNT(DISTINCT faculty_id) as present_count,
            SUM(CASE WHEN time_in IS NOT NULL THEN 1 ELSE 0 END) as checkins,
            SUM(CASE WHEN time_out IS NOT NULL THEN 1 ELSE 0 END) as checkouts
        FROM attendance
        WHERE date = ?
    ''', (today,))
    stats = cursor.fetchone()

    present_count = stats[0] if stats and stats[0] else 0
    checkins = stats[1] if stats and stats[1] else 0
    checkouts = stats[2] if stats and stats[2] else 0

    # Get recent activity (last 5 records)
    cursor.execute('''
        SELECT
            a.date,
            f.name,
            a.time_in,
            a.time_out
        FROM attendance a
        LEFT JOIN faculty f ON a.faculty_id = f.id
        ORDER BY a.date DESC, a.time_in DESC
        LIMIT 5
    ''')
    recent_activity = cursor.fetchall()

    # Format recent activity with duration
    formatted_activity = []
    for record in recent_activity:
        activity = {
            'date': record[0],
            'name': record[1],
            'time_in': record[2],
            'time_out': record[3],
            'duration': calculate_duration(record[2], record[3])
        }
        formatted_activity.append(activity)

    conn.close()

    return render_template('admin_dashboard.html',
                          faculty_count=faculty_count,
                          present_count=present_count,
                          checkins=checkins,
                          checkouts=checkouts,
                          recent_activity=formatted_activity)

@app.route('/faculty/dashboard')
@login_required
@faculty_required
def faculty_dashboard():
    """
    Faculty dashboard showing personal attendance records
    """
    if not current_user.faculty_id:
        return redirect(url_for('login', error='Faculty ID not found'))

    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get faculty information
    cursor.execute('''
        SELECT f.id, f.name
        FROM faculty f
        WHERE f.id = ?
    ''', (current_user.faculty_id,))
    faculty_data = cursor.fetchone()

    if not faculty_data:
        conn.close()
        return redirect(url_for('login', error='Faculty record not found'))

    faculty = {
        'id': faculty_data[0],
        'name': faculty_data[1]
    }

    # Get faculty attendance records
    cursor.execute('''
        SELECT
            a.date,
            f.name,
            a.time_in,
            a.time_out
        FROM attendance a
        LEFT JOIN faculty f ON a.faculty_id = f.id
        WHERE a.faculty_id = ?
        ORDER BY a.date DESC, a.time_in DESC
    ''', (current_user.faculty_id,))

    records = cursor.fetchall()

    # Format records with duration
    formatted_records = []
    for record in records:
        formatted_record = list(record)
        formatted_record.append(calculate_duration(record[2], record[3]))
        formatted_records.append(formatted_record)

    # Get recent activity (last 5 records)
    cursor.execute('''
        SELECT
            a.date,
            a.time_in,
            a.time_out
        FROM attendance a
        WHERE a.faculty_id = ?
        ORDER BY a.date DESC, a.time_in DESC
        LIMIT 5
    ''', (current_user.faculty_id,))

    recent_activity_data = cursor.fetchall()
    recent_activity = []

    for record in recent_activity_data:
        activity = {
            'date': record[0],
            'time_in': record[1],
            'time_out': record[2],
            'duration': calculate_duration(record[1], record[2])
        }
        recent_activity.append(activity)

    conn.close()

    # Get current month and year for DTR
    current_month = datetime.now().month
    current_year = datetime.now().year
    month_names = [calendar.month_name[i] for i in range(1, 13)]

    return render_template('faculty_dashboard.html',
                          faculty=faculty,
                          records=formatted_records,
                          recent_activity=recent_activity,
                          current_month=current_month,
                          current_year=current_year,
                          month_names=month_names)

# Initialize the database and create admin user if not exists
def init_admin():
    """Initialize admin user if not exists"""
    try:
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # Check if admin user exists
        cursor.execute('SELECT id FROM users WHERE role = ?', ('admin',))
        admin_exists = cursor.fetchone()

        if not admin_exists:
            # Create admin user with new credentials
            admin_username = 'Frass Admin'
            admin_password = 'cictfrassadmin00'
            hashed_password = generate_password_hash(admin_password)

            cursor.execute('''
                INSERT INTO users (username, password, role, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (admin_username, hashed_password, 'admin'))

            conn.commit()
            print(f"Created admin user with username: {admin_username}")
            print("PLEASE CHANGE THE DEFAULT PASSWORD IMMEDIATELY AFTER FIRST LOGIN!")

        conn.close()

    except Exception as e:
        print(f"Error initializing admin: {e}")

@app.route('/admin/view_faculty')
@login_required
@admin_required
def view_faculty():
    """
    View all faculty members
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get all faculty members with their user information
    cursor.execute('''
        SELECT
            f.id,
            f.name,
            f.position,
            f.contact,
            u.last_login
        FROM faculty f
        LEFT JOIN users u ON f.id = u.faculty_id
        ORDER BY f.name
    ''')

    faculty_data = cursor.fetchall()
    conn.close()

    faculty = []
    for row in faculty_data:
        # Check for images in different possible formats and locations
        image_paths = [
            f"Images/{row[0]}/profile.jpg",
            f"Images/{row[0]}.jpg",
            f"Images/{row[0]}.png"
        ]

        image_exists = False
        for path in image_paths:
            if os.path.exists(path):
                image_exists = True
                break

        faculty.append({
            'id': row[0],
            'name': row[1],
            'position': row[2],
            'contact': row[3],
            'last_login': row[4] if row[4] else 'Never',
            'has_image': image_exists
        })

    # Get current date and month names for DTR
    now = datetime.now()
    month_names = [calendar.month_name[i] for i in range(1, 13)]

    # Check if the current user is a super admin
    is_super_admin = current_user.is_super_admin()

    return render_template('view_faculty.html',
                          faculty=faculty,
                          now=now,
                          month_names=month_names,
                          is_super_admin=is_super_admin)

@app.route('/admin/edit_faculty/<faculty_id>', methods=['GET', 'POST'])
@login_required
@super_admin_required
def edit_faculty(faculty_id):
    """
    Edit faculty information
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name')
            department = request.form.get('department')
            position = request.form.get('position')
            contact = request.form.get('contact')

            # Validate data
            if not all([name, department]):
                return render_template('edit_faculty.html',
                                      faculty_id=faculty_id,
                                      faculty=None,
                                      error='Name and department are required')

            # Get current position before update
            cursor.execute('SELECT position FROM faculty WHERE id = ?', (faculty_id,))
            current_position_result = cursor.fetchone()
            current_position = current_position_result[0] if current_position_result else None

            # Update faculty information
            cursor.execute('''
                UPDATE faculty
                SET name = ?, department = ?, position = ?, contact = ?
                WHERE id = ?
            ''', (name, department, position, contact, faculty_id))

            # If position changed to Admin, update user role to admin_user
            if position == "Admin" and position != current_position:
                cursor.execute('''
                    UPDATE users
                    SET role = 'admin_user'
                    WHERE faculty_id = ? AND role = 'faculty'
                ''', (faculty_id,))
                print(f"Updated user role to admin_user for faculty {faculty_id}")
            # If position changed from Admin to something else, update role to faculty
            elif current_position == "Admin" and position != "Admin":
                cursor.execute('''
                    UPDATE users
                    SET role = 'faculty'
                    WHERE faculty_id = ? AND role = 'admin_user'
                ''', (faculty_id,))
                print(f"Updated user role to faculty for faculty {faculty_id}")

            conn.commit()

            # Check if a new image was uploaded
            if 'profile_image' in request.files and request.files['profile_image'].filename:
                profile_image = request.files['profile_image']

                # Save the image
                img_dir = os.path.join('Images', faculty_id)
                os.makedirs(img_dir, exist_ok=True)
                img_path = os.path.join(img_dir, 'profile.jpg')
                profile_image.save(img_path)

                # Copy image to static directory
                static_img_dir = os.path.join('static', 'faculty_images')
                os.makedirs(static_img_dir, exist_ok=True)
                static_img_path = os.path.join(static_img_dir, f'{faculty_id}.jpg')
                try:
                    img = Image.open(img_path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    img.save(static_img_path, 'JPEG')
                    print(f"Copied faculty image to static directory: {static_img_path}")
                except Exception as e:
                    print(f"Error copying faculty image to static directory: {e}")

                # Regenerate face encodings
                from EncodeGenerator import generate_encodings
                generate_encodings('Images')

            return redirect(url_for('view_faculty', success=f'Faculty {name} updated successfully'))

        except Exception as e:
            conn.rollback()
            print(f"Error updating faculty: {e}")
            return render_template('edit_faculty.html',
                                  faculty_id=faculty_id,
                                  faculty=None,
                                  error=f'Error updating faculty: {str(e)}')

    else:  # GET request
        try:
            # Get faculty information
            cursor.execute('''
                SELECT f.name, f.department, f.position, f.contact
                FROM faculty f
                WHERE f.id = ?
            ''', (faculty_id,))

            faculty_data = cursor.fetchone()

            if not faculty_data:
                conn.close()
                return redirect(url_for('view_faculty', error='Faculty not found'))

            faculty = {
                'id': faculty_id,
                'name': faculty_data[0],
                'department': faculty_data[1],
                'position': faculty_data[2],
                'contact': faculty_data[3]
            }

            conn.close()
            return render_template('edit_faculty.html', faculty=faculty)

        except Exception as e:
            print(f"Error retrieving faculty data: {e}")
            conn.close()
            return redirect(url_for('view_faculty', error=f'Error retrieving faculty data: {str(e)}'))

@app.route('/admin/reset_faculty_password/<faculty_id>', methods=['GET', 'POST'])
@login_required
@super_admin_required
def reset_faculty_password(faculty_id):
    """
    Reset a faculty member's password manually
    """
    if request.method == 'POST':
        try:
            new_password = request.form.get('new_password')
            confirm_password = request.form.get('confirm_password')

            if not new_password or not confirm_password:
                return render_template('reset_password.html', faculty_id=faculty_id, error='Please fill in all fields')

            if new_password != confirm_password:
                return render_template('reset_password.html', faculty_id=faculty_id, error='Passwords do not match')

            if len(new_password) < 8:
                return render_template('reset_password.html', faculty_id=faculty_id, error='Password must be at least 8 characters long')

            # Hash the new password
            hashed_password = generate_password_hash(new_password)

            # Update the user's password
            conn = sqlite3.connect('database_new.db')
            cursor = conn.cursor()

            # Get faculty name
            cursor.execute('SELECT name FROM faculty WHERE id = ?', (faculty_id,))
            result = cursor.fetchone()

            if not result:
                conn.close()
                return render_template('reset_password.html', faculty_id=faculty_id, error='Faculty not found')

            name = result[0]

            cursor.execute('UPDATE users SET password = ? WHERE faculty_id = ?', (hashed_password, faculty_id))

            if cursor.rowcount == 0:
                conn.close()
                return render_template('reset_password.html', faculty_id=faculty_id, error='Failed to reset password')

            conn.commit()
            conn.close()

            return redirect(url_for('view_faculty', success=f"Password for {name} has been reset successfully"))

        except Exception as e:
            print(f"Error resetting password: {e}")
            return render_template('reset_password.html', faculty_id=faculty_id, error=f'Error resetting password: {str(e)}')

    return render_template('reset_password.html', faculty_id=faculty_id)

@app.route('/admin/delete_faculty/<faculty_id>', methods=['POST'])
@login_required
@super_admin_required
def delete_faculty(faculty_id):
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    try:
        # Delete attendance records
        cursor.execute('DELETE FROM attendance WHERE faculty_id = ?', (faculty_id,))

        # Delete from current_session
        cursor.execute('DELETE FROM current_session WHERE faculty_id = ?', (faculty_id,))

        # Delete associated user account
        cursor.execute('DELETE FROM users WHERE faculty_id = ?', (faculty_id,))

        # Delete faculty record
        cursor.execute('DELETE FROM faculty WHERE id = ?', (faculty_id,))

        conn.commit()
    except Exception as e:
        print(f"Error deleting faculty: {e}")
        conn.rollback()
    finally:
        conn.close()

    return redirect(url_for('view_faculty'))

@app.route('/admin/faculty_attendance/<faculty_id>')
@login_required
@admin_required
def view_faculty_attendance(faculty_id):
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get faculty details
    cursor.execute('SELECT name FROM faculty WHERE id = ?', (faculty_id,))
    faculty_info = cursor.fetchone()

    if not faculty_info:
        conn.close()
        return redirect(url_for('view_faculty'))

    # Get faculty's attendance records
    cursor.execute('''
        SELECT
            a.date,
            f.name,
            a.time_in,
            a.time_out
        FROM attendance a
        LEFT JOIN faculty f ON a.faculty_id = f.id
        WHERE a.faculty_id = ?
        ORDER BY a.date DESC, a.time_in DESC
    ''', (faculty_id,))

    records = cursor.fetchall()
    conn.close()

    # Format records with duration
    formatted_records = []
    for record in records:
        formatted_record = list(record)
        formatted_record.append(calculate_duration(record[2], record[3]))
        formatted_records.append(formatted_record)

    return render_template('faculty_attendance.html',
                          records=formatted_records,
                          faculty_name=faculty_info[0],
                          faculty_id=faculty_id)

@app.route('/admin/view_faculty_images')
@login_required
@admin_required
def view_faculty_images():
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get all faculty members
    cursor.execute('''
        SELECT id, name
        FROM faculty
        ORDER BY name
    ''')
    faculty_rows = cursor.fetchall()
    conn.close()

    faculty_list = []
    for row in faculty_rows:
        faculty_id = row[0]

        # Check multiple possible paths for faculty images
        possible_paths = [
            os.path.join('Images', faculty_id, 'profile.jpg'),
            os.path.join('Images', f'{faculty_id}.jpg'),
            os.path.join('Images', f'{faculty_id}.png'),
            os.path.join('Images', '00003.png')  # Hardcoded for John Doe temporarily
        ]

        image_exists = False
        image_path = None

        for path in possible_paths:
            if os.path.exists(path):
                image_exists = True
                image_path = path
                break

        # Check if encoding exists
        is_encoded = False
        if os.path.exists('encodFile.p'):
            try:
                with open('encodFile.p', 'rb') as file:
                    encodeListKnownWithIds = pickle.load(file)
                    faculty_ids = encodeListKnownWithIds[1]
                    is_encoded = faculty_id in faculty_ids
            except Exception as e:
                print(f"Error checking encoding: {e}")

        faculty_list.append({
            'id': faculty_id,
            'name': row[1],
            'image_exists': image_exists,
            'is_encoded': is_encoded,
            'image_path': os.path.basename(image_path) if image_path else None
        })

    return render_template('view_faculty_images.html', faculty_list=faculty_list)

@app.route('/get_faculty_image/<faculty_id>')
@login_required
@admin_required
def get_faculty_image(faculty_id):
    # Try different possible image paths
    possible_paths = [
        os.path.join('Images', faculty_id, 'profile.jpg'),
        os.path.join('Images', f'{faculty_id}.jpg'),
        os.path.join('Images', f'{faculty_id}.png'),
        os.path.join('Images', '00003.png')  # Hardcoded for John Doe temporarily
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return send_file(path, mimetype='image/jpeg' if path.endswith('.jpg') else 'image/png')

    # If no image is found, return a default placeholder
    return 'Image not found', 404

@app.route('/faculty_image/<faculty_id>')
def faculty_image(faculty_id):
    """
    Serve faculty image with proper caching headers
    """
    # Check for image in Images directory first (most up-to-date)
    possible_paths = [
        os.path.join('Images', faculty_id, 'profile.jpg'),
        os.path.join('Images', f'{faculty_id}.jpg'),
        os.path.join('Images', f'{faculty_id}.png')
    ]

    for path in possible_paths:
        if os.path.exists(path):
            # If found, copy to static directory for future use
            try:
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                static_path = os.path.join('static', 'faculty_images', f'{faculty_id}.jpg')
                os.makedirs(os.path.dirname(static_path), exist_ok=True)
                img.save(static_path, 'JPEG')
                print(f"Updated faculty image from {path} to {static_path}")
                return send_file(static_path, mimetype='image/jpeg')
            except Exception as e:
                print(f"Error processing faculty image: {e}")

    # If no image found in Images directory, check static directory
    static_path = os.path.join('static', 'faculty_images', f'{faculty_id}.jpg')
    if os.path.exists(static_path):
        return send_file(static_path, mimetype='image/jpeg')

    # If no image found, serve the default image
    default_path = os.path.join('static', 'default-profile.jpg')
    if os.path.exists(default_path):
        return send_file(default_path, mimetype='image/jpeg')

    # If even the default image doesn't exist, return 404
    return 'Image not found', 404

@app.route('/download_csv')
@login_required
def download_csv():
    try:
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # If user is faculty, only get their records
        if current_user.role == 'faculty':
            cursor.execute('''
                SELECT
                    a.date,
                    a.time_in,
                    a.time_out,
                    f.name,
                    f.department
                FROM attendance a
                JOIN faculty f ON a.faculty_id = f.id
                WHERE a.faculty_id = ?
                ORDER BY a.date DESC, a.time_in DESC
            ''', (current_user.faculty_id,))
        else:
            # Admin can see all records
            cursor.execute('''
                SELECT
                    a.date,
                    a.time_in,
                    a.time_out,
                    f.name,
                    f.department
                FROM attendance a
                JOIN faculty f ON a.faculty_id = f.id
                ORDER BY a.date DESC, a.time_in DESC
            ''')

        records = cursor.fetchall()
        conn.close()

        # Create CSV in memory
        si = StringIO()
        cw = csv.writer(si)

        # Write headers
        cw.writerow(['Date', 'Time In', 'Time Out', 'Name', 'Department', 'Duration'])

        # Write records
        for record in records:
            date, time_in, time_out, name, department = record
            duration = calculate_duration(time_in, time_out)
            cw.writerow([date, time_in, time_out, name, department, duration])

        output = si.getvalue()
        si.close()

        # Generate filename with current date
        filename = f"attendance_records_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        return Response(
            output,
            mimetype="text/csv",
            headers={"Content-disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        print(f"Error generating CSV: {e}")
        flash('Error generating CSV file', 'error')
        return redirect(url_for('index'))

@app.route('/change_password', methods=['POST'])
@login_required
def change_password():
    """
    Change the user's password
    """
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if not all([current_password, new_password, confirm_password]):
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard', error='All fields are required'))
        else:
            return redirect(url_for('faculty_dashboard', error='All fields are required'))

    if new_password != confirm_password:
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard', error='New passwords do not match'))
        else:
            return redirect(url_for('faculty_dashboard', error='New passwords do not match'))

    if len(new_password) < 8:
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard', error='Password must be at least 8 characters long'))
        else:
            return redirect(url_for('faculty_dashboard', error='Password must be at least 8 characters long'))

    try:
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # Get current user password hash
        cursor.execute('SELECT password FROM users WHERE id = ?', (current_user.id,))
        current_hash = cursor.fetchone()[0]

        # Verify current password
        if not check_password_hash(current_hash, current_password):
            conn.close()
            if current_user.is_admin():
                return redirect(url_for('admin_dashboard', error='Current password is incorrect'))
            else:
                return redirect(url_for('faculty_dashboard', error='Current password is incorrect'))

        # Update password
        new_hash = generate_password_hash(new_password)
        cursor.execute('UPDATE users SET password = ? WHERE id = ?', (new_hash, current_user.id))
        conn.commit()
        conn.close()

        if current_user.is_admin():
            return redirect(url_for('admin_dashboard', success='Password changed successfully'))
        else:
            return redirect(url_for('faculty_dashboard', success='Password changed successfully'))

    except Exception as e:
        print(f"Error changing password: {e}")
        if current_user.is_admin():
            return redirect(url_for('admin_dashboard', error='An error occurred while changing password'))
        else:
            return redirect(url_for('faculty_dashboard', error='An error occurred while changing password'))

@app.route('/face-recognition')
def face_recognition_page():
    """
    Dedicated page for face recognition with video feed
    """
    return render_template('face_recognition.html')

@app.route('/view_dtr/<faculty_id>', methods=['GET', 'POST'])
@login_required
def view_dtr(faculty_id):
    """
    View Daily Time Record (DTR) for a faculty member without printing
    """
    # Check if user is admin or the same faculty
    if not current_user.is_admin() and current_user.faculty_id != faculty_id:
        return redirect(url_for('index'))

    # Get the month and year from the request, default to current month
    month = request.args.get('month', datetime.now().month)
    year = request.args.get('year', datetime.now().year)

    try:
        month = int(month)
        year = int(year)
    except ValueError:  
        month = datetime.now().month
        year = datetime.now().year

    # Validate month and year
    if month < 1 or month > 12:
        month = datetime.now().month
    if year < 2000 or year > 2100:
        year = datetime.now().year

    # Get the range of dates for the month
    _, last_day = calendar.monthrange(year, month)
    start_date = f"{year}-{month:02d}-01"
    end_date = f"{year}-{month:02d}-{last_day:02d}"

    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get faculty info (same as in print_dtr)
    cursor.execute('SELECT name, department, position FROM faculty WHERE id = ?', (faculty_id,))
    faculty_info = cursor.fetchone()

    if not faculty_info:
        conn.close()
        return redirect(url_for('index', error='Faculty not found'))

    faculty = {
        'id': faculty_id,
        'name': faculty_info[0],
        'department': faculty_info[1],
        'position': faculty_info[2] or '',
        'month_year': f"{calendar.month_name[month]} {year}"
    }

    # Create a dictionary with all days of the month
    attendance_by_day = {}
    for day in range(1, last_day + 1):
        attendance_by_day[day] = {
            'am_in': '',
            'am_out': '',
            'pm_in': '',
            'pm_out': ''
        }

    # Get all attendance records for the month - simplified query (same as in print_dtr)
    cursor.execute('''
        SELECT
            strftime('%d', date) as day,
            session,
            time_in,
            time_out
        FROM attendance
        WHERE faculty_id = ?
          AND date BETWEEN ? AND ?
        ORDER BY date, session
    ''', (faculty_id, start_date, end_date))

    attendance_data = cursor.fetchall()
    conn.close()

    # Format time function
    def format_time(time_str):
        if not time_str:
            return ''
        try:
            # First try to parse from 12-hour format with AM/PM
            time_obj = datetime.strptime(time_str, '%I:%M:%S %p')
            # Format as 6:09 without seconds and AM/PM
            return time_obj.strftime('%I:%M').lstrip('0')
        except ValueError:
            try:
                # Then try to parse from 24-hour format
                time_obj = datetime.strptime(time_str, '%H:%M:%S')
                # Format as 6:09 without seconds and AM/PM
                return time_obj.strftime('%I:%M').lstrip('0')  
            except ValueError:
                # Return as is if both parsing attempts fail
                return time_str

    # Process the attendance data (same as in print_dtr)
    for record in attendance_data:
        try:
            day = int(record[0])
            session = record[1]
            time_in = format_time(record[2])
            time_out = format_time(record[3])

            if session == 'morning':
                attendance_by_day[day]['am_in'] = time_in
                attendance_by_day[day]['am_out'] = time_out
            else:  # session == 'afternoon'
                attendance_by_day[day]['pm_in'] = time_in
                attendance_by_day[day]['pm_out'] = time_out
        except (ValueError, KeyError) as e:
            print(f"Error processing attendance record: {e}")
            continue

    # Calculate total hours (simplified to avoid errors)
    total_hours = 0
    total_minutes = 0

    # Get first day of week for the month
    first_day_of_week = calendar.monthrange(year, month)[0]

    return render_template('view_dtr_a4.html',
                          faculty=faculty,
                          attendance=attendance_by_day,
                          first_day_of_week=first_day_of_week,
                          days_in_month=last_day,
                          month=month,
                          year=year,
                          month_name=calendar.month_name[month],
                          total_hours=total_hours,
                          total_minutes=total_minutes)

@app.route('/print_dtr/<faculty_id>', methods=['GET', 'POST'])
@login_required
def print_dtr(faculty_id):
    """
    Generate Daily Time Record (DTR) for a faculty member
    """
    # Check if user is admin or the same faculty
    if not current_user.is_admin() and current_user.faculty_id != faculty_id:
        return redirect(url_for('index'))

    # Get the month and year from the request, default to current month
    month = request.args.get('month', datetime.now().month)
    year = request.args.get('year', datetime.now().year)

    try:
        month = int(month)
        year = int(year)
    except ValueError:
        month = datetime.now().month
        year = datetime.now().year

    # Validate month and year
    if month < 1 or month > 12:
        month = datetime.now().month
    if year < 2000 or year > 2100:
        year = datetime.now().year

    # Get the range of dates for the month
    _, last_day = calendar.monthrange(year, month)
    start_date = f"{year}-{month:02d}-01"
    end_date = f"{year}-{month:02d}-{last_day:02d}"

    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()

    # Get faculty info
    cursor.execute('SELECT name, department, position FROM faculty WHERE id = ?', (faculty_id,))
    faculty_info = cursor.fetchone()

    if not faculty_info:
        conn.close()
        return redirect(url_for('index', error='Faculty not found'))

    faculty = {
        'id': faculty_id,
        'name': faculty_info[0],
        'department': faculty_info[1],
        'position': faculty_info[2] or '',
        'month_year': f"{calendar.month_name[month]} {year}"
    }

    # Create a dictionary with all days of the month
    attendance_by_day = {}
    for day in range(1, last_day + 1):
        attendance_by_day[day] = {
            'am_in': '',
            'am_out': '',
            'pm_in': '',
            'pm_out': ''
        }

    # Get all attendance records for the month - simplified query
    cursor.execute('''
        SELECT
            strftime('%d', date) as day,
            session,
            time_in,
            time_out
        FROM attendance
        WHERE faculty_id = ?
          AND date BETWEEN ? AND ?
        ORDER BY date, session
    ''', (faculty_id, start_date, end_date))

    attendance_data = cursor.fetchall()
    conn.close()

    # Format time function
    def format_time(time_str):
        if not time_str:
            return ''
        try:
            # First try to parse from 12-hour format with AM/PM
            time_obj = datetime.strptime(time_str, '%I:%M:%S %p')
            # Format as 6:09 without seconds and AM/PM
            return time_obj.strftime('%I:%M').lstrip('0')
        except ValueError:
            try:
                # Then try to parse from 24-hour format
                time_obj = datetime.strptime(time_str, '%H:%M:%S')
                # Format as 6:09 without seconds and AM/PM
                return time_obj.strftime('%I:%M').lstrip('0')
            except ValueError:
                # Return as is if both parsing attempts fail
                return time_str

    # Process the attendance data
    for record in attendance_data:
        try:
            day = int(record[0])
            session = record[1]
            time_in = format_time(record[2])
            time_out = format_time(record[3])

            if session == 'morning':
                attendance_by_day[day]['am_in'] = time_in
                attendance_by_day[day]['am_out'] = time_out
            else:  # session == 'afternoon'
                attendance_by_day[day]['pm_in'] = time_in
                attendance_by_day[day]['pm_out'] = time_out
        except (ValueError, KeyError) as e:
            print(f"Error processing attendance record: {e}")
            continue

    # Calculate total hours (simplified to avoid errors)
    total_hours = 0
    total_minutes = 0

    # Get first day of week for the month
    first_day_of_week = calendar.monthrange(year, month)[0]

    print(f"Attendance data for faculty {faculty_id}: {attendance_by_day}")

    return render_template('DTRA4SIZE.html',
                          faculty=faculty,
                          attendance=attendance_by_day,
                          first_day_of_week=first_day_of_week,
                          days_in_month=last_day,
                          month=month,
                          year=year,
                          month_name=calendar.month_name[month],
                          total_hours=total_hours,
                          total_minutes=total_minutes)

@app.route('/get_faculty_details/<faculty_id>')
def get_faculty_details(faculty_id):
    """
    API endpoint to get faculty details including name, department, position, and other info
    """
    try:
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # Get faculty data
        cursor.execute('''
            SELECT name, position, email
            FROM faculty
            WHERE id = ?
        ''', (faculty_id,))

        faculty_data = cursor.fetchone()
        conn.close()

        if not faculty_data:
            return jsonify({"error": "Faculty not found"}), 404

        # Check if faculty image exists
        image_path = os.path.join('static', 'faculty_images', f'{faculty_id}.jpg')
        has_image = os.path.exists(image_path)

        # Prepare response data
        response = {
            "id": faculty_id,
            "name": faculty_data[0],
            "position": faculty_data[1] if faculty_data[1] else "",
            "email": faculty_data[2] if len(faculty_data) > 2 and faculty_data[2] else "",
            "image_url": url_for('static', filename=f'faculty_images/{faculty_id}.jpg') if has_image else url_for('static', filename='default-profile.jpg')
        }

        return jsonify(response)

    except Exception as e:
        print(f"Error fetching faculty details: {e}")
        return jsonify({"error": str(e)}), 500

def update_db_schema():
    """Update the database schema to support the new admin_user role"""
    try:
        conn = sqlite3.connect('database_new.db')
        cursor = conn.cursor()

        # Check if we need to update the schema
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()

        # Find the role column and its constraints
        for col in columns:
            if col[1] == 'role':
                # If the constraint doesn't include admin_user, we need to update
                if 'admin_user' not in col[2]:
                    # SQLite doesn't support altering constraints directly,
                    # so we need to recreate the table with the new constraint

                    # 1. Rename the current table
                    cursor.execute("ALTER TABLE users RENAME TO users_old")

                    # 2. Create a new table with the updated constraint
                    cursor.execute('''
                        CREATE TABLE users (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username TEXT UNIQUE NOT NULL,
                            password TEXT NOT NULL,
                            role TEXT NOT NULL CHECK(role IN ('admin', 'admin_user', 'faculty')),
                            faculty_id TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            last_login TIMESTAMP,
                            FOREIGN KEY (faculty_id) REFERENCES faculty (id)
                        )
                    ''')

                    # 3. Copy data from old table to new table
                    cursor.execute('''
                        INSERT INTO users (id, username, password, role, faculty_id, created_at, last_login)
                        SELECT id, username, password, role, faculty_id, created_at, last_login FROM users_old
                    ''')

                    # 4. Drop the old table
                    cursor.execute("DROP TABLE users_old")

                    print("Updated users table schema to include 'admin_user' role")
                break

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error updating database schema: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return False

# Call the update function during initialization
update_db_schema()
init_db()
init_admin()

if __name__ == '__main__':
    app.run(debug=True, port=7777, host='0.0.0.0')
