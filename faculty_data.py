import sqlite3

def init_faculty_db():
    """
    Initializes the SQLite database for storing faculty data.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    
    # Check if position and email columns exist
    cursor.execute("PRAGMA table_info(faculty)")
    columns = [column[1] for column in cursor.fetchall()]
    
    # Create the faculty table if it doesn't exist
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS faculty (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            department TEXT,
            position TEXT,
            email TEXT
        )
    ''')
    
    # Add position column if it doesn't exist
    if 'position' not in columns:
        try:
            cursor.execute('ALTER TABLE faculty ADD COLUMN position TEXT')
            print("Added position column to faculty table")
        except sqlite3.OperationalError:
            print("Position column already exists")
    
    # Add email column if it doesn't exist
    if 'email' not in columns:
        try:
            cursor.execute('ALTER TABLE faculty ADD COLUMN email TEXT')
            print("Added email column to faculty table")
        except sqlite3.OperationalError:
            print("Email column already exists")
    
    conn.commit()
    conn.close()

def add_faculty(id, name, department, position=None, email=None):
    """
    Adds a new faculty member to the SQLite database.
    If the faculty ID already exists, the record is ignored.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR IGNORE INTO faculty (id, name, department, position, email)
        VALUES (?, ?, ?, ?, ?)
    ''', (id, name, department, position, email))
    conn.commit()
    conn.close()
    print(f"Faculty {name} added successfully (or already exists).")

def update_faculty(id, name=None, department=None, position=None, email=None):
    """
    Updates an existing faculty member in the SQLite database.
    Only provided fields will be updated.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    
    # First, get current values
    cursor.execute('SELECT name, department, position, email FROM faculty WHERE id = ?', (id,))
    current = cursor.fetchone()
    
    if current:
        # Update only fields that were provided
        name = name if name is not None else current[0]
        department = department if department is not None else current[1]
        position = position if position is not None else current[2]
        email = email if email is not None else current[3]
        
        cursor.execute('''
            UPDATE faculty 
            SET name = ?, department = ?, position = ?, email = ?
            WHERE id = ?
        ''', (name, department, position, email, id))
        conn.commit()
        print(f"Faculty {id} updated successfully.")
    else:
        print(f"Faculty {id} not found for update.")
    
    conn.close()

def get_faculty_name(faculty_id):
    """
    Fetches the faculty name from the database using the faculty ID.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT name FROM faculty WHERE id = ?
    ''', (faculty_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else "Unknown"

def get_faculty_info(faculty_id):
    """
    Fetches complete faculty information from the database.
    """
    conn = sqlite3.connect('database_new.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, name, department, position, email
        FROM faculty 
        WHERE id = ?
    ''', (faculty_id,))
    result = cursor.fetchone()
    conn.close()
    
    if result:
        return {
            'id': result[0],
            'name': result[1],
            'department': result[2] if result[2] else '',
            'position': result[3] if result[3] else '',
            'email': result[4] if result[4] else ''
        }
    else:
        return None

# Initialize the faculty database
init_faculty_db()
