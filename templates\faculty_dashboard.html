{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    {% if error %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> {{ error }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Faculty Dashboard -->
    <div class="row mb-4">
        <div class="col d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-user-graduate"></i> Faculty Dashboard</h2>
            <div>
                <a href="{{ url_for('download_csv') }}" class="btn btn-success me-2">
                    <i class="fas fa-download"></i> Download CSV
                </a>
                <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#viewDTRModal">
                    <i class="fas fa-eye"></i> View DTR
                </button>
                <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#printDTRModal">
                    <i class="fas fa-print"></i> Print DTR
                </button>
                <button type="button" class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                    <i class="fas fa-key"></i> Change Password
                </button>
                <a href="{{ url_for('logout') }}" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleCurrentPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="savePasswordBtn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View DTR Modal -->
    <div class="modal fade" id="viewDTRModal" tabindex="-1" aria-labelledby="viewDTRModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewDTRModalLabel">View Daily Time Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="viewDtrForm" action="{{ url_for('view_dtr', faculty_id=faculty.id) }}" method="GET">
                        <div class="mb-3">
                            <label for="view_month" class="form-label">Month</label>
                            <select class="form-select" id="view_month" name="month">
                                {% for m in range(1, 13) %}
                                    <option value="{{ m }}" {% if m == current_month %}selected{% endif %}>
                                        {{ month_names[m-1] }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="view_year" class="form-label">Year</label>
                            <select class="form-select" id="view_year" name="year">
                                {% for y in range(current_year-5, current_year+1) %}
                                    <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>
                                        {{ y }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="viewDtrForm" class="btn btn-info">View DTR</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Print DTR Modal -->
    <div class="modal fade" id="printDTRModal" tabindex="-1" aria-labelledby="printDTRModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printDTRModalLabel">Print Daily Time Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="dtrForm" action="{{ url_for('print_dtr', faculty_id=faculty.id) }}" method="GET" target="_blank">
                        <div class="mb-3">
                            <label for="month" class="form-label">Month</label>
                            <select class="form-select" id="month" name="month">
                                {% for m in range(1, 13) %}
                                    <option value="{{ m }}" {% if m == current_month %}selected{% endif %}>
                                        {{ month_names[m-1] }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="year" class="form-label">Year</label>
                            <select class="form-select" id="year" name="year">
                                {% for y in range(current_year-5, current_year+1) %}
                                    <option value="{{ y }}" {% if y == current_year %}selected{% endif %}>
                                        {{ y }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="dtrForm" class="btn btn-primary">Generate DTR</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Faculty Information Card -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user"></i> Your Information</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-grow-1">
                            <h5 class="mb-1">{{ faculty.name }}</h5>
                            <p class="text-muted mb-0">{{ faculty.email }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Recent Activity</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time In</th>
                                    <th>Time Out</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recent_activity %}
                                <tr>
                                    <td>{{ record.date }}</td>
                                    <td>{{ record.time_in }}</td>
                                    <td>{{ record.time_out }}</td>
                                    <td>{{ record.duration }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Records -->
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Your Attendance Records</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time In</th>
                                    <th>Time Out</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceRecords">
                                {% for record in records %}
                                <tr>
                                    <td>{{ record[0] }}</td>
                                    <td>{{ record[2] }}</td>
                                    <td>{{ record[3] }}</td>
                                    <td>{{ record[4] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const password = document.querySelector(inputId);
            const button = document.querySelector(buttonId);

            button.addEventListener('click', function() {
                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                password.setAttribute('type', type);

                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
        }

        // Initialize password toggles
        togglePasswordVisibility('#current_password', '#toggleCurrentPassword');
        togglePasswordVisibility('#new_password', '#toggleNewPassword');
        togglePasswordVisibility('#confirm_password', '#toggleConfirmPassword');

        // Handle change password form submission
        document.getElementById('savePasswordBtn').addEventListener('click', function() {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            // Validate passwords
            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('Please fill in all password fields');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('New passwords do not match');
                return;
            }

            if (newPassword.length < 8) {
                alert('New password must be at least 8 characters long');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('current_password', currentPassword);
            formData.append('new_password', newPassword);
            formData.append('confirm_password', confirmPassword);

            // Send the request to change password
            fetch('{{ url_for("change_password") }}', {
                method: 'POST',
                body: formData,
                redirect: 'follow'
            })
            .then(response => {
                if (response.redirected) {
                    // If redirected, follow the redirect
                    window.location.href = response.url;
                    return;
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        // Close the modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                        modal.hide();
                        // Clear the form
                        document.getElementById('changePasswordForm').reset();
                        // Reload the page to show success message
                        window.location.reload();
                    } else {
                        alert(data.error || 'Failed to change password');
                    }
                } catch (e) {
                    // If not JSON, assume it's a redirect
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // If there's an error but the password was changed, just reload
                window.location.reload();
            });
        });
    });
</script>
{% endblock %}