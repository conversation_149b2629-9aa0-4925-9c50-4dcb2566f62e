{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-plus"></i> Register New Faculty</h5>
                </div>
                <div class="card-body">
                    {% if error %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle"></i> {{ error }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}

                    {% if success %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle"></i> {{ success }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endif %}

                    <form method="POST" action="{{ url_for('register') }}" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Left Column - Faculty Information -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-user"></i> Faculty Information</h5>
                                <hr>

                                <div class="mb-3">
                                    <label for="faculty_id" class="form-label">Faculty ID</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                        <input type="text" class="form-control" id="faculty_id" name="faculty_id" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                </div>

                                <!-- Department field removed -->
                                <input type="hidden" name="department" value="CICT">

                                <div class="mb-3">
                                    <label for="position" class="form-label">Position</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                        <select class="form-select" id="position" name="position" required>
                                            <option value="" selected disabled>Select position</option>
                                            <option value="Staff">Staff</option>
                                            <option value="Faculty">Faculty</option>
                                            <option value="Admin">Admin</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="contact_number" class="form-label">Contact Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="text" class="form-control" id="contact_number" name="contact_number" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Authentication Information -->
                            <div class="col-md-6">
                                <h5><i class="fas fa-lock"></i> Authentication Information</h5>
                                <hr>

                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Password must be at least 8 characters long.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="profile_image" class="form-label">Profile Image</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-image"></i></span>
                                        <input type="file" class="form-control" id="profile_image" name="profile_image" required accept="image/*">
                                    </div>
                                    <div class="form-text">Upload a clear frontal face image for recognition.</div>
                                </div>

                                <!-- Admin type section removed as there is only one super admin -->
                                <input type="hidden" name="admin_type" value="admin_user">
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12 d-flex justify-content-between">
                                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Register Faculty
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const password = document.querySelector(inputId);
            const button = document.querySelector(buttonId);

            button.addEventListener('click', function() {
                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                password.setAttribute('type', type);

                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
        }

        // Initialize password toggles
        togglePasswordVisibility('#password', '#togglePassword');
        togglePasswordVisibility('#confirm_password', '#toggleConfirmPassword');

        // Admin type section is now always visible if user is super admin
    });
</script>
{% endblock %}