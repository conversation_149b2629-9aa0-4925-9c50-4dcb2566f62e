{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-users"></i> Faculty Members</h2>
            <div>
                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    {% if success %}
    <div class="alert alert-success">{{ success }}</div>
    {% endif %}

    {% if error %}
    <div class="alert alert-danger">{{ error }}</div>
    {% endif %}

    <!-- Faculty Table -->
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> Faculty List</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Faculty ID</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Contact</th>
                            <th>Last Login</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if faculty %}
                            {% for f in faculty %}
                            <tr>
                                <td>{{ f.id }}</td>
                                <td>{{ f.name }}</td>
                                <td>{{ f.position }}</td>
                                <td>{{ f.contact }}</td>
                                <td>{{ f.last_login }}</td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('view_faculty_attendance', faculty_id=f.id) }}" class="btn btn-outline-secondary" title="View attendance">
                                            <i class="fas fa-clock"></i> Attendance
                                        </a>
                                        <a href="{{ url_for('view_dtr', faculty_id=f.id) }}" class="btn btn-outline-info" title="View DTR">
                                            <i class="fas fa-eye"></i> View DTR
                                        </a>
                                        <a href="{{ url_for('print_dtr', faculty_id=f.id) }}" class="btn btn-outline-secondary" title="Print DTR">
                                            <i class="fas fa-print"></i> Print DTR
                                        </a>
                                        {% if is_super_admin %}
                                        <a href="{{ url_for('edit_faculty', faculty_id=f.id) }}" class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="{{ url_for('reset_faculty_password', faculty_id=f.id) }}" class="btn btn-outline-warning" title="Reset password">
                                            <i class="fas fa-key"></i> Reset
                                        </a>
                                        <button type="button" class="btn btn-outline-danger"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteModal{{ f.id }}"
                                                title="Delete">
                                            <i class="fas fa-trash-alt"></i> Delete
                                        </button>

                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal{{ f.id }}" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Are you sure you want to delete <strong>{{ f.name }}</strong>?</p>
                                                        <p class="text-danger">This action cannot be undone.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form action="{{ url_for('delete_faculty', faculty_id=f.id) }}" method="POST">
                                                            <button type="submit" class="btn btn-danger">Delete</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center">No faculty members found</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}