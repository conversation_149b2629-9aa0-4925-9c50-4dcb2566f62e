{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-clock"></i> Attendance Records</h2>
            <div class="d-flex gap-2">
                <a href="{{ url_for('download_csv') }}" class="btn btn-success">
                    <i class="fas fa-download"></i> Download CSV
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-calendar-check"></i> Total Days</h5>
                    <h2 class="card-text">{{ records|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-sign-in-alt"></i> Total Check-ins</h5>
                    <h2 class="card-text">{{ records|selectattr('3')|list|length }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-sign-out-alt"></i> Total Check-outs</h5>
                    <h2 class="card-text">{{ records|selectattr('4')|list|length }}</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Table -->
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                <th>Date</th>
                            {% if current_user.role == 'admin' %}
                            <th>Faculty Name</th>
                            <!-- Department column removed -->
                            {% endif %}
                <th>Time In</th>
                <th>Time Out</th>
                            <th>Duration</th>
                            <th>Status</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr>
                <td>{{ record[0] }}</td>
                            {% if current_user.role == 'admin' %}
                            <td>{{ record[1] or 'Unknown' }}</td>
                            <!-- Department column removed -->
                            {% endif %}
                            <td>{{ record[3] or '-' }}</td>
                            <td>{{ record[4] or '-' }}</td>
                <td>{{ record[5] }}</td>
                            <td>
                                {% if record[3] and record[4] %}
                                <span class="badge bg-success">Completed</span>
                                {% elif record[3] %}
                                <span class="badge bg-warning">Checked In</span>
                                {% else %}
                                <span class="badge bg-danger">Absent</span>
                                {% endif %}
                            </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card {
        display: none !important;
    }
    .table {
        border-collapse: collapse;
    }
    .table th, .table td {
        border: 1px solid #dee2e6;
    }
}

.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-5px);
}

.table th {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.9em;
    padding: 0.5em 0.8em;
}

.btn {
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}
</style>
{% endblock %}
